#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def filter_suitable_skills():
    """过滤出适合武器和装备的技能"""
    
    # 读取技能数据
    with open('skills_data.json', 'r', encoding='utf-8') as f:
        skills_data = json.load(f)
    
    # 不适合的技能关键词（黑名单）
    blacklist_keywords = [
        # 敏感技能
        'Sensitive', 'sensitive',
        # 敌人技能
        '_enemy', 'enemy_', 'Enemy',
        # Boss技能
        '_boss', 'boss_', 'Boss',
        # 天气和环境
        'rain', 'heat', 'weather', '天气',
        # GM和测试
        'gmMask', 'GM', 'test', 'Test',
        # 系统状态
        'Buff', 'buff', 'State_',
        # 特殊标记
        '面具', 'mask', 'Mask',
        # 死亡相关
        'die', 'Die', 'kill', 'Kill', 'death',
        # 蝙蝠状态等特殊状态
        'flySkyBat', 'Bat',
        # 子弹碰撞等系统技能
        'bulletRain', 'collision',
        # 狙击王等任务专用
        'sniperKing', 'King',
        # 其他不合适的
        'phantom', 'world180', 'blackHole'
    ]
    
    # 白名单技能类别（这些是肯定可以用的）
    whitelist_categories = [
        '105heroSkillClass',    # 英雄技能
        '152outfitSkillClass',  # 时装技能
        '162peakSkillClass',    # 巅峰技能
        '187yingSkillClass',    # 小樱技能
        '246partsSkillClass',   # 零件技能
        '294equipSkillClass',   # 装备技能
        '324armsSkillClass',    # 武器技能
        '62weaponSkillClass'    # 副手技能
    ]
    
    suitable_skills = []
    
    # 遍历所有技能类别
    for category, skills in skills_data.items():
        print(f"处理类别: {category}")
        
        for skill in skills:
            skill_name = skill.get('name', '')
            if not skill_name:
                continue
            
            # 检查是否在黑名单中
            is_blacklisted = False
            for keyword in blacklist_keywords:
                if keyword in skill_name:
                    is_blacklisted = True
                    break
            
            if is_blacklisted:
                print(f"  跳过黑名单技能: {skill_name}")
                continue
            
            # 如果是白名单类别，直接添加
            if category in whitelist_categories:
                suitable_skills.append(skill_name)
                print(f"  添加白名单技能: {skill_name}")
            else:
                # 其他类别需要更严格的筛选
                # 只添加明显安全的技能
                safe_keywords = [
                    '群体', '狂暴', '反击', '派生', '吞噬', '定点', '远视', '隐身',
                    '万弹', '金刚钻', '毒雾', '嗜爪', '欺凌', '魅惑', '电离',
                    '馈赠', '沉默', '近视', '先锋', '溅射', '自燃', '精准',
                    '藐视', '元素', '血盾', '滑翔', '翻滚', '王者', '附身',
                    '尖叫', '反转', '妖魅', '智慧', '技能复制', '尸化',
                    '战争抗体', '坏血', '猎手', '悦动', '饥饿', '血脉',
                    '沃龙', '鹰眼', '遁形', '核爆', '注视', '搏命',
                    '怒蜂', '群蜂', '精锐', '强击', '奉献', '防御',
                    '载具', '靠近', '猎击', '酸性', '爆裂', '月饼',
                    '修罗', '旋风', '震地', '狂刃', '补给', '弹性',
                    '牺牲', '光环', '护佑', '轰炸', '电流', '退化',
                    '充能', '辐射', '静电', '敏捷', '眩晕', '失血',
                    '闪烁', '自燃', '先锋', '反转', '怒火', '麻痹',
                    '爆石', '顽强', '复原', '致残', '减速', '猎人',
                    '克星', '屠刀', '上帝', '鬼步', '闪避', '抵挡',
                    '相同', '攻击力', '伤害', '雪藏', '近战', '千斤顶',
                    '血液', '分身', '风暴', '钩拉', '腐蚀', '飞雪',
                    '雪引', '爆怒', '踢爆', '召唤', '冲撞', '飓风',
                    '摩卡', '护体', '免疫', '磁力', '嗜爪', '瘴气',
                    '耐久', '钢背', '负离子', '净化', '芒刺', '荆棘',
                    '折射', '装甲', '反制', '真空', '加速', '合金',
                    '敏捷', '真情', '重生', '回复', '治愈', '分子',
                    '血肉', '雷霆', '野性', '巨力', '嘲讽', '遇强',
                    '复仇', '共鸣', '暗夜', '恶爪', '击毙', '剧毒',
                    '溅射', '振奋', '致残', '致盲', '派生', '震动',
                    '背刺', '裂空', '穿刺', '弹力', '盛血', '膝跳',
                    '静电', '迷失', '共振', '老练', '远射', '超重',
                    '绝灭', '快感', '七步', '引爆', '刷新', '闪电',
                    '超级', '炎爆', '冷凝', '蚀骨', '痛击', '连弩',
                    '粘性', '火焰', '贯穿', '无限', '跳斩', '风雷',
                    '影灭', '爆沙', '爆胆', '爆震', '破甲', '溃甲',
                    '飞镰', '跟踪', '战修罗', '突袭', '惩罚', '打滑',
                    '共振', '子母弹', '烟花', '陈年', '轨迹', '聚合',
                    '核弹', '核动力', '冻血', '守望', '外壳', '冲击',
                    '见面礼', '降低', '特效', '提升', '抵挡', '元素',
                    '掉率', '震地', '爆发', '冥刃', '金钟', '驱散',
                    '加持', '崩溃', '石海', '能量', '防御', '抵抗',
                    '聚合', '封锁', '枷锁', '击落', '复飞', '坠落',
                    '统治', '利刃', '陨石', '闪电', '破宠', '致盲',
                    '巨伤', '尸毒', '防空', '激光', '复仇', '夺命',
                    '反击', '旋转', '短命', '折寿', '薄命', '全域',
                    '竖盾', '强电', '锁血', '监禁', '禁无双', '禁火炮',
                    '击落', '衰竭', '闲逛', '同葬', '加速', '狂飙',
                    '颠倒', '集体', '暗无', '跟屁虫', '火种'
                ]
                
                is_safe = False
                for keyword in safe_keywords:
                    if keyword in skill_name:
                        is_safe = True
                        break
                
                if is_safe:
                    suitable_skills.append(skill_name)
                    print(f"  添加安全技能: {skill_name}")
                else:
                    print(f"  跳过不确定技能: {skill_name}")
    
    print(f"\n总共筛选出 {len(suitable_skills)} 个适合的技能")
    
    # 生成ActionScript数组格式
    as_array = '[\n'
    for i, name in enumerate(suitable_skills):
        if i == len(suitable_skills) - 1:
            as_array += f'               "{name}"\n'
        else:
            as_array += f'               "{name}",\n'
    as_array += '            ]'
    
    # 保存到文件
    with open('suitable_skills.txt', 'w', encoding='utf-8') as f:
        f.write("适合武器和装备的技能 (ActionScript数组格式):\n\n")
        f.write(as_array)
        f.write("\n\n技能列表:\n")
        for i, name in enumerate(suitable_skills, 1):
            f.write(f"{i}. {name}\n")
    
    print("适合的技能已保存到 suitable_skills.txt")
    return suitable_skills

if __name__ == "__main__":
    filter_suitable_skills()