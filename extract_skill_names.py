#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def extract_skill_names():
    """从skills_data.json中提取所有技能的英文名称"""
    
    # 读取技能数据
    with open('skills_data.json', 'r', encoding='utf-8') as f:
        skills_data = json.load(f)
    
    all_skill_names = []
    
    # 遍历所有技能类别
    for category, skills in skills_data.items():
        print(f"处理类别: {category}")
        for skill in skills:
            skill_name = skill.get('name', '')
            if skill_name:
                all_skill_names.append(skill_name)
    
    print(f"总共提取到 {len(all_skill_names)} 个技能")
    
    # 生成ActionScript数组格式
    as_array = '[\n'
    for i, name in enumerate(all_skill_names):
        if i == len(all_skill_names) - 1:
            as_array += f'               "{name}"\n'
        else:
            as_array += f'               "{name}",\n'
    as_array += '            ]'
    
    # 保存到文件
    with open('all_skill_names.txt', 'w', encoding='utf-8') as f:
        f.write("所有技能英文名称 (ActionScript数组格式):\n\n")
        f.write(as_array)
        f.write("\n\n技能列表:\n")
        for i, name in enumerate(all_skill_names, 1):
            f.write(f"{i}. {name}\n")
    
    print("技能名称已保存到 all_skill_names.txt")
    return all_skill_names

if __name__ == "__main__":
    extract_skill_names()