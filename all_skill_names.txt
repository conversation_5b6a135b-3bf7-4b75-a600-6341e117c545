所有技能英文名称 (ActionScript数组格式):

[
               "群体圣光",
               "狂暴",
               "反击",
               "派生导弹",
               "吞噬",
               "定点轰炸",
               "远视",
               "群体隐身",
               "万弹归宗",
               "金刚钻",
               "毒雾",
               "嗜爪",
               "欺凌",
               "魅惑",
               "电离折射",
               "馈赠",
               "沉默",
               "近视",
               "先锋盾",
               "全局溅射",
               "群体自燃",
               "精准",
               "FoggyHero",
               "藐视",
               "元素叠加",
               "血盾",
               "滑翔-不可被重造",
               "rolling_hero",
               "王者之翼",
               "附身",
               "silverScreen_hero",
               "隐匿之雾",
               "尖叫",
               "反转术",
               "全域圣光",
               "妖魅",
               "wisdomAnger_hero",
               "技能复制",
               "尸化",
               "毒雾-减血",
               "吞噬-回血",
               "反馈-反弹被动技能",
               "selfBurn_hero_link",
               "wisdomAnger_hero_link",
               "rolling_hero_link",
               "沉默-使我方技能免疫3秒",
               "moreMissile_hero_dizziness",
               "moreMissile_hero_dizziness2",
               "战争抗体",
               "outfit_blood",
               "猎手原形",
               "悦动先锋",
               "饥饿行踪",
               "血脉沸腾",
               "沃龙隐",
               "沃龙隐-攻击力加成",
               "鹰眼",
               "遁形",
               "核爆",
               "gaze_peak",
               "sacrifice_peak",
               "怒蜂",
               "群蜂",
               "hurtAddYing",
               "trueshotYing",
               "dedicationLove",
               "resistPerLove2",
               "defenceAddLove",
               "vehicleAddLove30",
               "resistPerLoveAdd2",
               "nearAddLifeLove",
               "mainResistPerLove",
               "增加血量",
               "增加弹药",
               "无敌药水",
               "invinCannedDrop",
               "hurtDrugDrop",
               "增加移动速度",
               "addMocha",
               "zombieBottle",
               "glutinous",
               "iconGiftBox",
               "iconGiftBoxBuff",
               "godHiding_things",
               "godHiding_Pet",
               "moonCake",
               "tangyuan",
               "nineCake",
               "jiaozi",
               "armsDropDouble",
               "equipDropDouble",
               "armsDropDoubleAndGem",
               "equipDropDoubleAndEquipGem",
               "highPetCard",
               "highVehicleCard",
               "highCardState",
               "highPartnerCard",
               "highPartnerState",
               "electromagnet",
               "superSpreadCard",
               "skeletonCard",
               "skeletonCard_link",
               "pumpkinHead",
               "wolfFashionSkill",
               "goldFalcon",
               "dragonHeadSkill",
               "zombieMask",
               "crazy_sanji",
               "xiaoBoShoot",
               "xiaoMingShoot",
               "瞬秒-小怪",
               "xiaoAiShoot",
               "shotgunBladeHero",
               "chinaCaptainSkill",
               "armyCommanderSkill",
               "snowShadowSkill",
               "foxLingSkill",
               "bladeSkill",
               "hundredGhostsSkill",
               "cyanArmySkill",
               "生化锁定",
               "highDrill",
               "superHighDrill",
               "highDrillHit",
               "unendBuff",
               "addChargerMax2",
               "extendCd",
               "竞技场-伤害加倍",
               "击中减速",
               "减速光环",
               "主动加血",
               "反弹伤害",
               "火花面具",
               "沃龙面具",
               "丛安",
               "rollingFast",
               "speedUpTask",
               "超级散射",
               "无限馈赠",
               "极限射速",
               "战争狂人脱下头盔",
               "丛林特种兵技能",
               "瞬秒所有",
               "attackNoDodge",
               "findHide",
               "sniperKingBuff",
               "bulletRainBallHit",
               "flySkyBatBuff",
               "rifleSensitive",
               "sniperSensitive",
               "shotgunSensitive",
               "pistolSensitive",
               "rocketSensitive",
               "crossbowSensitive",
               "flamerSensitive",
               "laserSensitive",
               "otherSensitive",
               "weaponSensitive",
               "vehicleSensitive",
               "petSensitive",
               "redArmsSensitive",
               "handSensitive",
               "猎击",
               "酸性腐蚀",
               "爆裂弹",
               "月饼子弹",
               "修罗弹夹",
               "狂战尸-旋风刀",
               "狂战尸-震地",
               "狂战尸-狂刃追踪",
               "远视",
               "helmet_PetZombieFootball",
               "橄榄僵尸-弹性世界",
               "全域圣光",
               "sacrifice_PetIronChief",
               "defenceAuras_PetIronChief",
               "godHand_PetIronChief",
               "无尽轰炸",
               "current_skull",
               "degradation_PetBoomSkull",
               "charged_PetLake",
               "lightBall_PetLake",
               "static_PetLake",
               "agile_PetLake",
               "lightBall_PetLake_slow",
               "灼热视线-眩晕",
               "laser_PetFightWolf_extra",
               "闪烁-目标点爆炸",
               "selfBurn_pet",
               "狂暴",
               "沉默",
               "群体圣光",
               "反击",
               "电离折射",
               "馈赠",
               "pioneer_hero",
               "电离反转",
               "毒雾",
               "定点轰炸",
               "欺凌",
               "flash_pet",
               "gngerFire_pet",
               "paralysis_pet",
               "imploding_pet",
               "strong_pet",
               "trueshot_pet",
               "recoveryHalo_pet",
               "disabledHalo_pet",
               "slowMoveHalo_pet",
               "人类克星",
               "恶魔猎手",
               "恶魔屠刀",
               "godEyes",
               "godMace",
               "heroSprint",
               "godEyesDefence",
               "godMaceDefence",
               "petHurtHole",
               "towerHurter",
               "cdMulMei",
               "cdHoleMei",
               "angerHoleMei",
               "dieOffMei",
               "dieNiuBiMei",
               "degradationMei",
               "dodgeProZang",
               "defenceZang",
               "hurtStrikerZang",
               "noHurtZang",
               "sameArmsHurtAddZang",
               "hurtZang",
               "hurtHoleZang",
               "godHand_equip",
               "immune_equip",
               "magneticField_equip",
               "strongHalo_equip",
               "murderous_equip",
               "poisonRange_equip",
               "attackSpeedHalo_equip",
               "sacrifice_equip",
               "backStrong_equip",
               "anionSkin_equip",
               "treater_equip",
               "backWeak_equip",
               "thornSkin_equip",
               "refraction_equip",
               "zoomOut",
               "refraction_equip_link",
               "underMurderous_jie",
               "vacuumJie",
               "armsSpeed_jie",
               "合金护甲",
               "真空-无敌",
               "敏捷馈赠",
               "防御力提升",
               "攻击力提升",
               "真情之力",
               "rebirth_loveSkill",
               "回复",
               "真情治愈",
               "element_loveSkill",
               "elementLifeBack_loveSkill",
               "嘲讽",
               "遇强则刚",
               "复仇",
               "共鸣",
               "暗夜信徒",
               "恶爪",
               "tauntLingBack_link",
               "revengeLing_link",
               "击中减速",
               "击毙补充弹药",
               "击毙回复",
               "剧毒",
               "击毙溅射",
               "振奋",
               "击中溅射",
               "致残",
               "击中麻痹",
               "致盲",
               "击中回复",
               "击中沉默",
               "击中派生",
               "震动炮",
               "背刺",
               "implodingGod",
               "裂空波",
               "穿刺",
               "弹力回收",
               "盛血绞杀",
               "膝跳反应",
               "静电反应",
               "迷失电流",
               "共振",
               "老练",
               "远射",
               "超重",
               "瞬秒",
               "绝灭",
               "快感",
               "搏命",
               "七步毒",
               "Hit_pointBoom_godArmsSkill",
               "Hit_fleshSkill_godArmsSkill",
               "Hit_imploding_godArmsSkill",
               "击中闪电",
               "击中眩晕",
               "超级派生",
               "超级减速",
               "超级麻痹",
               "炎爆",
               "冷凝",
               "蚀骨",
               "痛击",
               "连弩",
               "viscous_ArmsSkill",
               "超级粘性",
               "火焰始祖",
               "贯穿波",
               "无限火力",
               "跳斩",
               "上帝之杖",
               "风雷",
               "影灭",
               "imploding_blackArmsSkill",
               "爆胆",
               "爆震",
               "飞镰",
               "跟踪飞镰",
               "战修罗",
               "突袭",
               "惩罚",
               "打滑",
               "超级共振",
               "lash_ArmsSkill",
               "beadCrossbow_ArmsSkill",
               "陈年老糖",
               "editBulletPath",
               "炎爆-爆",
               "爆胆-无法叠加得buff",
               "sickle_godArmsSkill2_link",
               "轰天聚合",
               "镇山聚合",
               "霸空聚合",
               "核弹头",
               "核动力",
               "守望之盾",
               "合金外壳",
               "strongStrong",
               "shockWave",
               "meetingGift",
               "狂战射手-击中麻痹",
               "特效",
               "特效",
               "防御力提升30%",
               "noHurt10Lings",
               "resistPerLing4",
               "hurtStrikerLing",
               "hurtLing40",
               "vehicleLing40",
               "eleStrikerLing",
               "dropStrikerLing",
               "weaponEmp",
               "weaponEmpAll",
               "goldSpadeSkill",
               "凯撒特效附带",
               "凯撒特效附带",
               "stoneSeaBuff",
               "shootSpeed2",
               "reduceBulletHurtBy",
               "先锋盾",
               "分身",
               "demCloned2",
               "weaponDefence",
               "weaponNo",
               "fitVehicleDefence",
               "immune",
               "offAllSkill",
               "offAllSkill5",
               "offPassSkill",
               "toLand",
               "toFlyCd",
               "underToLand",
               "统治圈",
               "利刃盾",
               "利刃罩",
               "陨石雨",
               "大地闪电",
               "破宠",
               "blindnessSuper",
               "电离驱散",
               "无敌驱散",
               "超级散射",
               "巨伤盾",
               "尸毒",
               "防空盾",
               "激光盾",
               "复仇之箭",
               "复仇之魂",
               "夺命箭",
               "夺命魂",
               "瞬秒所有除了载具",
               "反击导弹",
               "旋转电球",
               "旋转电球",
               "短命之仇",
               "shortLivedDisabledUnderHit",
               "summonShortLife",
               "summonShortLifeMax",
               "killAllSummon",
               "verShield",
               "verShieldBuff",
               "midLightning",
               "godShield",
               "lockLife",
               "killXinLing",
               "cantMove",
               "noPurgoldArms",
               "noRocket",
               "everToLand",
               "衰竭",
               "blackHoleDemon",
               "strollCard",
               "击中沉默",
               "weDieTogather",
               "fastCd",
               "fastCd2",
               "world180",
               "phantomDemon",
               "worldBlack",
               "rain1",
               "rain2",
               "rain3",
               "heat1",
               "heat2",
               "heat3",
               "killToSnakeTail",
               "dieSnakeTail",
               "madFireBody"
            ]

技能列表:
1. 群体圣光
2. 狂暴
3. 反击
4. 派生导弹
5. 吞噬
6. 定点轰炸
7. 远视
8. 群体隐身
9. 万弹归宗
10. 金刚钻
11. 毒雾
12. 嗜爪
13. 欺凌
14. 魅惑
15. 电离折射
16. 馈赠
17. 沉默
18. 近视
19. 先锋盾
20. 全局溅射
21. 群体自燃
22. 精准
23. FoggyHero
24. 藐视
25. 元素叠加
26. 血盾
27. 滑翔-不可被重造
28. rolling_hero
29. 王者之翼
30. 附身
31. silverScreen_hero
32. 隐匿之雾
33. 尖叫
34. 反转术
35. 全域圣光
36. 妖魅
37. wisdomAnger_hero
38. 技能复制
39. 尸化
40. 毒雾-减血
41. 吞噬-回血
42. 反馈-反弹被动技能
43. selfBurn_hero_link
44. wisdomAnger_hero_link
45. rolling_hero_link
46. 沉默-使我方技能免疫3秒
47. moreMissile_hero_dizziness
48. moreMissile_hero_dizziness2
49. 战争抗体
50. outfit_blood
51. 猎手原形
52. 悦动先锋
53. 饥饿行踪
54. 血脉沸腾
55. 沃龙隐
56. 沃龙隐-攻击力加成
57. 鹰眼
58. 遁形
59. 核爆
60. gaze_peak
61. sacrifice_peak
62. 怒蜂
63. 群蜂
64. hurtAddYing
65. trueshotYing
66. dedicationLove
67. resistPerLove2
68. defenceAddLove
69. vehicleAddLove30
70. resistPerLoveAdd2
71. nearAddLifeLove
72. mainResistPerLove
73. 增加血量
74. 增加弹药
75. 无敌药水
76. invinCannedDrop
77. hurtDrugDrop
78. 增加移动速度
79. addMocha
80. zombieBottle
81. glutinous
82. iconGiftBox
83. iconGiftBoxBuff
84. godHiding_things
85. godHiding_Pet
86. moonCake
87. tangyuan
88. nineCake
89. jiaozi
90. armsDropDouble
91. equipDropDouble
92. armsDropDoubleAndGem
93. equipDropDoubleAndEquipGem
94. highPetCard
95. highVehicleCard
96. highCardState
97. highPartnerCard
98. highPartnerState
99. electromagnet
100. superSpreadCard
101. skeletonCard
102. skeletonCard_link
103. pumpkinHead
104. wolfFashionSkill
105. goldFalcon
106. dragonHeadSkill
107. zombieMask
108. crazy_sanji
109. xiaoBoShoot
110. xiaoMingShoot
111. 瞬秒-小怪
112. xiaoAiShoot
113. shotgunBladeHero
114. chinaCaptainSkill
115. armyCommanderSkill
116. snowShadowSkill
117. foxLingSkill
118. bladeSkill
119. hundredGhostsSkill
120. cyanArmySkill
121. 生化锁定
122. highDrill
123. superHighDrill
124. highDrillHit
125. unendBuff
126. addChargerMax2
127. extendCd
128. 竞技场-伤害加倍
129. 击中减速
130. 减速光环
131. 主动加血
132. 反弹伤害
133. 火花面具
134. 沃龙面具
135. 丛安
136. rollingFast
137. speedUpTask
138. 超级散射
139. 无限馈赠
140. 极限射速
141. 战争狂人脱下头盔
142. 丛林特种兵技能
143. 瞬秒所有
144. attackNoDodge
145. findHide
146. sniperKingBuff
147. bulletRainBallHit
148. flySkyBatBuff
149. rifleSensitive
150. sniperSensitive
151. shotgunSensitive
152. pistolSensitive
153. rocketSensitive
154. crossbowSensitive
155. flamerSensitive
156. laserSensitive
157. otherSensitive
158. weaponSensitive
159. vehicleSensitive
160. petSensitive
161. redArmsSensitive
162. handSensitive
163. 猎击
164. 酸性腐蚀
165. 爆裂弹
166. 月饼子弹
167. 修罗弹夹
168. 狂战尸-旋风刀
169. 狂战尸-震地
170. 狂战尸-狂刃追踪
171. 远视
172. helmet_PetZombieFootball
173. 橄榄僵尸-弹性世界
174. 全域圣光
175. sacrifice_PetIronChief
176. defenceAuras_PetIronChief
177. godHand_PetIronChief
178. 无尽轰炸
179. current_skull
180. degradation_PetBoomSkull
181. charged_PetLake
182. lightBall_PetLake
183. static_PetLake
184. agile_PetLake
185. lightBall_PetLake_slow
186. 灼热视线-眩晕
187. laser_PetFightWolf_extra
188. 闪烁-目标点爆炸
189. selfBurn_pet
190. 狂暴
191. 沉默
192. 群体圣光
193. 反击
194. 电离折射
195. 馈赠
196. pioneer_hero
197. 电离反转
198. 毒雾
199. 定点轰炸
200. 欺凌
201. flash_pet
202. gngerFire_pet
203. paralysis_pet
204. imploding_pet
205. strong_pet
206. trueshot_pet
207. recoveryHalo_pet
208. disabledHalo_pet
209. slowMoveHalo_pet
210. 人类克星
211. 恶魔猎手
212. 恶魔屠刀
213. godEyes
214. godMace
215. heroSprint
216. godEyesDefence
217. godMaceDefence
218. petHurtHole
219. towerHurter
220. cdMulMei
221. cdHoleMei
222. angerHoleMei
223. dieOffMei
224. dieNiuBiMei
225. degradationMei
226. dodgeProZang
227. defenceZang
228. hurtStrikerZang
229. noHurtZang
230. sameArmsHurtAddZang
231. hurtZang
232. hurtHoleZang
233. godHand_equip
234. immune_equip
235. magneticField_equip
236. strongHalo_equip
237. murderous_equip
238. poisonRange_equip
239. attackSpeedHalo_equip
240. sacrifice_equip
241. backStrong_equip
242. anionSkin_equip
243. treater_equip
244. backWeak_equip
245. thornSkin_equip
246. refraction_equip
247. zoomOut
248. refraction_equip_link
249. underMurderous_jie
250. vacuumJie
251. armsSpeed_jie
252. 合金护甲
253. 真空-无敌
254. 敏捷馈赠
255. 防御力提升
256. 攻击力提升
257. 真情之力
258. rebirth_loveSkill
259. 回复
260. 真情治愈
261. element_loveSkill
262. elementLifeBack_loveSkill
263. 嘲讽
264. 遇强则刚
265. 复仇
266. 共鸣
267. 暗夜信徒
268. 恶爪
269. tauntLingBack_link
270. revengeLing_link
271. 击中减速
272. 击毙补充弹药
273. 击毙回复
274. 剧毒
275. 击毙溅射
276. 振奋
277. 击中溅射
278. 致残
279. 击中麻痹
280. 致盲
281. 击中回复
282. 击中沉默
283. 击中派生
284. 震动炮
285. 背刺
286. implodingGod
287. 裂空波
288. 穿刺
289. 弹力回收
290. 盛血绞杀
291. 膝跳反应
292. 静电反应
293. 迷失电流
294. 共振
295. 老练
296. 远射
297. 超重
298. 瞬秒
299. 绝灭
300. 快感
301. 搏命
302. 七步毒
303. Hit_pointBoom_godArmsSkill
304. Hit_fleshSkill_godArmsSkill
305. Hit_imploding_godArmsSkill
306. 击中闪电
307. 击中眩晕
308. 超级派生
309. 超级减速
310. 超级麻痹
311. 炎爆
312. 冷凝
313. 蚀骨
314. 痛击
315. 连弩
316. viscous_ArmsSkill
317. 超级粘性
318. 火焰始祖
319. 贯穿波
320. 无限火力
321. 跳斩
322. 上帝之杖
323. 风雷
324. 影灭
325. imploding_blackArmsSkill
326. 爆胆
327. 爆震
328. 飞镰
329. 跟踪飞镰
330. 战修罗
331. 突袭
332. 惩罚
333. 打滑
334. 超级共振
335. lash_ArmsSkill
336. beadCrossbow_ArmsSkill
337. 陈年老糖
338. editBulletPath
339. 炎爆-爆
340. 爆胆-无法叠加得buff
341. sickle_godArmsSkill2_link
342. 轰天聚合
343. 镇山聚合
344. 霸空聚合
345. 核弹头
346. 核动力
347. 守望之盾
348. 合金外壳
349. strongStrong
350. shockWave
351. meetingGift
352. 狂战射手-击中麻痹
353. 特效
354. 特效
355. 防御力提升30%
356. noHurt10Lings
357. resistPerLing4
358. hurtStrikerLing
359. hurtLing40
360. vehicleLing40
361. eleStrikerLing
362. dropStrikerLing
363. weaponEmp
364. weaponEmpAll
365. goldSpadeSkill
366. 凯撒特效附带
367. 凯撒特效附带
368. stoneSeaBuff
369. shootSpeed2
370. reduceBulletHurtBy
371. 先锋盾
372. 分身
373. demCloned2
374. weaponDefence
375. weaponNo
376. fitVehicleDefence
377. immune
378. offAllSkill
379. offAllSkill5
380. offPassSkill
381. toLand
382. toFlyCd
383. underToLand
384. 统治圈
385. 利刃盾
386. 利刃罩
387. 陨石雨
388. 大地闪电
389. 破宠
390. blindnessSuper
391. 电离驱散
392. 无敌驱散
393. 超级散射
394. 巨伤盾
395. 尸毒
396. 防空盾
397. 激光盾
398. 复仇之箭
399. 复仇之魂
400. 夺命箭
401. 夺命魂
402. 瞬秒所有除了载具
403. 反击导弹
404. 旋转电球
405. 旋转电球
406. 短命之仇
407. shortLivedDisabledUnderHit
408. summonShortLife
409. summonShortLifeMax
410. killAllSummon
411. verShield
412. verShieldBuff
413. midLightning
414. godShield
415. lockLife
416. killXinLing
417. cantMove
418. noPurgoldArms
419. noRocket
420. everToLand
421. 衰竭
422. blackHoleDemon
423. strollCard
424. 击中沉默
425. weDieTogather
426. fastCd
427. fastCd2
428. world180
429. phantomDemon
430. worldBlack
431. rain1
432. rain2
433. rain3
434. heat1
435. heat2
436. heat3
437. killToSnakeTail
438. dieSnakeTail
439. madFireBody
