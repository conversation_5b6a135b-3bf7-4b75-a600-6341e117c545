@echo off
echo 正在编译玉帝后台版...
echo 修改内容：显示所有231个技能而不是只显示少数技能
echo.

if not exist "scripts玉帝后台版\Main.as" (
    echo 错误：找不到 Main.as 文件
    pause
    exit /b 1
)

mxmlc -output scripts.swf -source-path scripts玉帝后台版 -library-path+=lib -main Main.as

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 编译成功！
    echo ========================================
    echo 修改说明：
    echo 1. 武器编辑界面现在应该显示所有231个技能
    echo 2. 技能列表标题会显示实际技能数量
    echo 3. 绕过了noRandomListB的限制
    echo.
    echo 请将生成的 scripts.swf 文件复制到游戏目录中测试
    echo ========================================
) else (
    echo.
    echo 编译失败，请检查错误信息
)
pause
