{"105heroSkillClass": [{"name": "群体圣光", "cnName": "群体圣光", "father": "heroSkill", "index": "0", "description": "单位技能释放后，回复单位周围[range]码以内的所有友方单位[mul]的生命值，每个单位都有[obj.pro]的概率直接回满生命值。", "cd": "120", "effectType": "life", "conditionType": "active", "target": "me,range,we", "duration": "", "mul": "0.3", "range": "300", "iconUrl": ""}, {"name": "狂暴", "cnName": "狂暴", "father": "heroSkill", "index": "0", "description": "释放技能后，单位增加[mul-1]的射击速度（面板射速大于10发每秒的，不加射速，增加同等伤害），同时不消耗弹药，持续[duration]秒。无视封锁。", "cd": "100", "effectType": "crazyHeroSkill", "conditionType": "active", "target": "me", "duration": "5", "mul": "2", "range": "", "iconUrl": ""}, {"name": "反击", "cnName": "反击", "father": "heroSkill", "index": "0", "description": "释放技能后，单位开启[mul]的吸血状态，持续[duration]秒。", "cd": "100", "effectType": "suckBlood", "conditionType": "active", "target": "me", "duration": "8", "mul": "0.3", "range": "", "iconUrl": ""}, {"name": "派生导弹", "cnName": "派生导弹", "father": "heroSkill", "index": "0", "description": "每次射击都有[effectProArr.0]的概率派生跟踪导弹，修罗模式下概率大幅降低。导弹伤害为当前武器伤害值的[mul]。{font color='#00FF00'}14级无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "bullet_hitMissile", "conditionType": "passive", "target": "me", "duration": "", "mul": "1", "range": "", "iconUrl": ""}, {"name": "吞噬", "cnName": "吞噬", "father": "heroSkill", "index": "0", "description": "吞噬单位附近[range]码以内的一个敌方普通单位，接下来每秒回复[mul]的生命值，持续20秒。找不到目标不消耗技能次数。", "cd": "120", "effectType": "devour", "conditionType": "active", "target": "me,near,enemy,normal", "duration": "", "mul": "0.13", "range": "300", "iconUrl": ""}, {"name": "定点轰炸", "cnName": "定点轰炸", "father": "heroSkill", "index": "0", "description": "轰炸鼠标位置100码范围内的敌人，伤害为当前武器实际战斗力的1.5倍。有[effectProArr.1]的几率释放2次。", "cd": "20", "effectType": "bullet", "conditionType": "active", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "远视", "cnName": "远视", "father": "heroSkill", "index": "0", "description": "距离目标越远伤害越大，大于[range]码开始加成，每隔100码增加[mul]的伤害，最大增加[value]倍。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "target", "duration": "", "mul": "0.05", "range": "200", "iconUrl": ""}, {"name": "群体隐身", "cnName": "群体隐身", "father": "heroSkill", "index": "0", "description": "释放技能后，所有我方单位进入隐身状态，持续[duration]秒。你对敌人发起攻击会打破隐身状态，同时造成2~4倍的暴击。", "cd": "", "effectType": "hidingB", "conditionType": "active", "target": "me,range,we,,hero", "duration": "12", "mul": "4", "range": "99999", "iconUrl": ""}, {"name": "万弹归宗", "cnName": "万弹归宗", "father": "heroSkill", "index": "0", "description": "召集[range]范围内的队友，每人释放出10颗导弹，每颗导弹的伤害等于当前武器实际战斗力值的75%，并且有[effectProArr.1]的几率释放2次。", "cd": "140", "effectType": "bullet", "conditionType": "active", "target": "me,range,we,,,5", "duration": "", "mul": "1", "range": "500", "iconUrl": ""}, {"name": "金刚钻", "cnName": "金刚钻", "father": "heroSkill", "index": "0", "description": "释放技能后，提升[range]码内友方单位[mul-1]的攻击力，同时他们射出的子弹都将无视地形，不消耗弹药（非修罗模式），持续[duration]秒。", "cd": "80", "effectType": "penetrationGapAnNoBullet", "conditionType": "active", "target": "me,range,we,,hero", "duration": "8", "mul": "1.1", "range": "300", "iconUrl": ""}, {"name": "毒雾", "cnName": "毒雾", "father": "heroSkill", "index": "0", "description": "在单位周围释放毒雾，感染[range]码以内的所有敌人（包括首领），使他们降低90%移动速度并且每秒减少1%生命值，持续[duration]秒。", "cd": "120", "effectType": "poisonousFog_hero", "conditionType": "active", "target": "mouse,range,enemy", "duration": "10", "mul": "0.3", "range": "200", "iconUrl": ""}, {"name": "嗜爪", "cnName": "嗜爪", "father": "heroSkill", "index": "0", "description": "释放技能后，提升周围[range]码内所有我方单的攻击力至原先的[mul]，持续[duration]秒。", "cd": "100", "effectType": "murderous_addHurtMul2", "conditionType": "active", "target": "me,range,we", "duration": "8", "mul": "1.5", "range": "300", "iconUrl": ""}, {"name": "欺凌", "cnName": "欺凌", "father": "heroSkill", "index": "0", "description": "攻击生命低于20%的敌人，会对它造成[mul]的伤害。", "cd": "", "effectType": "bullying_hero", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.5", "range": "", "iconUrl": ""}, {"name": "魅惑", "cnName": "魅惑", "father": "heroSkill", "index": "0", "description": "魅惑鼠标附近的敌人（非携枪的小怪或者非修罗模式下的精英怪），使其加入我方阵营，有[effectProArr.1]的概率同时魅惑2个敌人。", "cd": "130", "effectType": "charm", "conditionType": "active", "target": "mouse,near,enemy,normal_superNoDemon,normal", "duration": "120", "mul": "", "range": "300", "iconUrl": ""}, {"name": "电离折射", "cnName": "电离折射", "father": "heroSkill", "index": "0", "description": "释放技能后，单位将[mul]的伤害反弹给敌人，自身不受到伤害，持续5秒。反弹伤害不超过目标最大生命值的1/5；目标生命值少于20%时不受反弹伤害。", "cd": "90", "effectType": "noUnderHurtB", "conditionType": "active", "target": "me", "duration": "5", "mul": "2", "range": "", "iconUrl": ""}, {"name": "馈赠", "cnName": "馈赠", "father": "heroSkill", "index": "0", "description": "每次单位释放完主动技能，就有[effectProArr.0]的几率大幅度回复该主动技能的冷却时间。", "cd": "", "effectType": "heroSkillFullCd", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "沉默", "cnName": "沉默", "father": "heroSkill", "index": "0", "description": "使[range]码以内的所有敌方单位无法释放技能，持续[duration]秒，同时清除目标身上的隐身、狂暴、嗜爪、电离折射、电离反转的状态。", "cd": "60", "effectType": "silenceBAndClearState", "conditionType": "active", "target": "me,range,enemy", "duration": "5", "mul": "", "range": "400", "iconUrl": ""}, {"name": "近视", "cnName": "近视", "father": "heroSkill", "index": "0", "description": "距离目标越近伤害越大，小于[range]码开始加成，每隔60码增加[mul]的伤害，最大增加[value]倍。{font color='#00FF00'}14级无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "target", "duration": "", "mul": "0.05", "range": "400", "iconUrl": ""}, {"name": "先锋盾", "cnName": "先锋盾", "father": "heroSkill", "index": "0", "description": "单位生命值大于[obj.per]时，受到的伤害减少[1-mul]。{font color='#00FF00'}14级无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "pioneer_hero", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.7", "range": "", "iconUrl": ""}, {"name": "全局溅射", "cnName": "全局溅射", "father": "heroSkill", "index": "0", "description": "释放技能后，单位进入全局溅射状态，子弹无论碰到敌人还是地面都将对子弹周围[range]码的敌人造成[mul]的溅射伤害，最多对10个敌人造成伤害。状态持续[duration]秒。", "cd": "100", "effectType": "globalSpurting_hero", "conditionType": "active", "target": "me", "duration": "6", "mul": "0.5", "range": "600", "iconUrl": ""}, {"name": "群体自燃", "cnName": "群体自燃", "father": "heroSkill", "index": "0", "description": "使周围[range]码内的友方单位全身燃起熊熊烈火，每0.1秒随机对附近300码范围内的一个敌人造成伤害，伤害值为当前武器面板战斗力的0.5x[mul]。状态持续[duration]秒。", "cd": "90", "effectType": "no", "conditionType": "active", "target": "me,range,we", "duration": "6", "mul": "0.10", "range": "700", "iconUrl": ""}, {"name": "精准", "cnName": "精准", "father": "heroSkill", "index": "0", "description": "提升爆头[mul]的伤害值。{font color='#00FF00'}14级无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "target", "duration": "", "mul": "0.16", "range": "", "iconUrl": ""}, {"name": "FoggyHero", "cnName": "抵御", "father": "heroSkill", "index": "", "description": "每次受伤都有[effectProArr.0]的概率无敌[duration]秒。", "cd": "", "effectType": "FoggyDefence", "conditionType": "passive", "target": "me", "duration": "0.1", "mul": "", "range": "", "iconUrl": ""}, {"name": "藐视", "cnName": "藐视", "father": "heroSkill", "index": "0", "description": "藐视鼠标位置附近的1个敌人，使其攻击力降低[mul]，防御力降低[secMul]，持续[duration]秒。", "cd": "10", "effectType": "lookDown", "conditionType": "active", "target": "mouse,near,enemy", "duration": "10", "mul": "0.05", "range": "9000", "iconUrl": ""}, {"name": "元素叠加", "cnName": "元素叠加", "father": "heroSkill", "index": "0", "description": "把藏师当前武器[mul]的元素伤害，叠加到队友具有同类元素伤害的武器上。", "cd": "", "effectType": "eleOverlap", "conditionType": "passive", "target": "me,range,we", "duration": "0.5", "mul": "0.09", "range": "9999999", "iconUrl": ""}, {"name": "血盾", "cnName": "血盾", "father": "heroSkill", "index": "0", "description": "在战斗中每消灭1个怪物将使自己的防御力提升[mul]，最大提升200%。", "cd": "", "effectType": "bloodShield", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "0.05", "range": "", "iconUrl": ""}, {"name": "滑翔-不可被重造", "cnName": "滑翔", "father": "heroSkill", "index": "0", "description": "在空中按下“蹲下”键后，人物将开启滑翔模式，降低[1-secMul]下落速度，并提高[mul]的射击速度，落地后技能效果消失。{font color='#00FF00'}升级至第9级后，无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "gliding", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "0.04", "range": "", "iconUrl": "SkillIcon/gliding_hero"}, {"name": "rolling_hero", "cnName": "翻滚", "father": "heroSkill", "index": "", "description": "人物蹲下时，双击移动按键可向前翻滚[value]码，期间不受攻击、子弹碰撞，降低途经敌人30%的攻击力，最短触发间隔[minTriggerT]秒。{font color='#00FF00'}升级至第9级后，无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "hero<PERSON><PERSON>ing", "conditionType": "passive", "target": "me", "duration": "0.5", "mul": "0", "range": "", "iconUrl": "SkillIcon/rolling_hero"}, {"name": "王者之翼", "cnName": "王者之翼", "father": "heroSkill", "index": "0", "description": "玩家在空中的伤害增加[mul-1]。{font color='#00FF00'}无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "changeHurt_air", "conditionType": "passive", "target": "me", "duration": "", "mul": "1.3", "range": "", "iconUrl": ""}, {"name": "附身", "cnName": "附身", "father": "heroSkill", "index": "", "description": "附身鼠标处的我方队友，控制其攻击、技能，持续[duration]秒。6级之后在非主线任务中可附身敌方首领。双击载具键可退出。", "cd": "120", "effectType": "possession", "conditionType": "active", "target": "alertTarget", "duration": "6", "mul": "1.5", "range": "100", "iconUrl": ""}, {"name": "silverScreen_hero", "cnName": "全域光波", "father": "heroSkill", "index": "", "description": "释放两道全域光波，瞬秒被击中的小怪，减少精英怪15%的生命值，减少首领5%的生命值（当前生命值不低于10%），无视技能免疫，无视敏感技能。", "cd": "70", "effectType": "bullet_silverScreen", "conditionType": "active", "target": "me", "duration": "", "mul": "3", "range": "", "iconUrl": "SkillIcon/silverScreen"}, {"name": "隐匿之雾", "cnName": "隐匿之雾", "father": "heroSkill", "index": "0", "description": "使自己进入隐身状态，并获得[mul]的防御力，持续[duration]秒。隐身单位攻击敌人或者受到敌人攻击都不会打破隐身状态。", "cd": "40", "effectType": "invisibility_hero", "conditionType": "active", "target": "me", "duration": "7", "mul": "0.5", "range": "", "iconUrl": ""}, {"name": "尖叫", "cnName": "尖叫", "father": "heroSkill", "index": "0", "description": "对周围[range]码范围内的敌人造成惊吓，使受到惊吓的单位完全失去攻击能力并加快速度逃离施法者。持续[duration]秒。", "cd": "80", "effectType": "screaming_hero", "conditionType": "active", "target": "me,range,enemy", "duration": "5", "mul": "1.6", "range": "400", "iconUrl": ""}, {"name": "反转术", "cnName": "反转术", "father": "heroSkill", "index": "0", "description": "使[range]码范围内的友方单位进入电离反转状态，拥有该状态的单位所受到伤害都会转化为自己的生命值。持续6秒。", "cd": "90", "effectType": "reverseHurt", "conditionType": "active", "target": "me,range,we", "duration": "6", "mul": "", "range": "700", "iconUrl": ""}, {"name": "全域圣光", "cnName": "全域圣光", "father": "heroSkill", "index": "0", "description": "单位技能释放后，回复所有友方单位70%的生命值。", "cd": "115", "effectType": "life", "conditionType": "active", "target": "me,range,we", "duration": "", "mul": "0.7", "range": "9999", "iconUrl": ""}, {"name": "妖魅", "cnName": "妖魅", "father": "heroSkill", "index": "0", "description": "使距离鼠标点最近的敌方单位（非携枪的普通怪或者非修罗模式下的精英怪）加入我方阵营，被魅惑的单位回满生命值并随机获得1~2个全队身上的技能，获得2个技能的概率为[mul]。", "cd": "100", "effectType": "coquettish_hero", "conditionType": "active", "target": "mouse,near,enemy,normal_superNoDemon,normal", "duration": "", "mul": "0", "range": "300", "iconUrl": ""}, {"name": "wisdomAnger_hero", "cnName": "智慧怒火", "father": "heroSkill", "index": "0", "description": "单位释放任何主动技能，都可以使周围[range]码内的友方单位燃起紫火，每0.1秒随机对附近300码内的1个敌人造成伤害，伤害值为当前武器面板战斗力的[mul]，持续[duration]秒。{font color='#00FF00'}升级至第14级后，无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "wisdomAnger_hero", "conditionType": "passive", "target": "me,range,we", "duration": "8", "mul": "0.05", "range": "700", "iconUrl": ""}, {"name": "技能复制", "cnName": "技能复制", "father": "heroSkill", "index": "0", "description": "复制鼠标附近敌人（第10级开始可复制队友）的一个技能，无视技能免疫。不能复制分身、夺命箭、夺命魂、旋转电球等技能，不能复制带有指定动画的技能。", "cd": "50", "effectType": "skillCopy_hero", "conditionType": "active", "target": "mouse,near,enemy", "duration": "", "mul": "", "range": "300", "iconUrl": ""}, {"name": "尸化", "cnName": "尸化", "father": "heroSkill", "index": "0", "description": "当生命值低于20%时，小美尸化成僵尸，攻击速度提升[mul-1]，防御力提升原来的[value]倍，技能回复速度提升[secMul-1]。生命值高于20%，且变身时间超过[range]秒时将恢复人类外观。", "cd": "20", "effectType": "changeT<PERSON><PERSON><PERSON>ie_hero", "conditionType": "active", "target": "me", "duration": "999999", "mul": "1.2", "range": "20", "iconUrl": ""}, {"name": "毒雾-减血", "cnName": "毒雾-减血", "father": "heroSkillLink", "index": "0", "description": "", "cd": "", "effectType": "poison_xiaoAi", "conditionType": "active", "target": "mouse,range,enemy", "duration": "10", "mul": "0.01", "range": "200", "iconUrl": ""}, {"name": "吞噬-回血", "cnName": "吞噬-回血", "father": "heroSkillLink", "index": "0", "description": "", "cd": "", "effectType": "lifeRate", "conditionType": "active", "target": "me", "duration": "20", "mul": "0.13", "range": "", "iconUrl": ""}, {"name": "反馈-反弹被动技能", "cnName": "电离折射", "father": "heroSkillLink", "index": "0", "description": "", "cd": "", "effectType": "backHurt", "conditionType": "passive", "target": "target", "duration": "", "mul": "1", "range": "", "iconUrl": ""}, {"name": "selfBurn_hero_link", "cnName": "自燃-链接", "father": "heroSkillLink", "index": "0", "description": "单位全身燃起熊熊烈火，对附近[range]范围内的敌人造成伤害，伤害值为当前武器面板战斗力的[mul]。", "cd": "", "effectType": "normal_hurt", "conditionType": "passive", "target": "me,random,enemy", "duration": "0.1", "mul": "0.05", "range": "300", "iconUrl": ""}, {"name": "wisdomAnger_hero_link", "cnName": "智慧怒火-链接", "father": "heroSkillLink", "index": "0", "description": "单位全身燃起熊熊烈火，对附近[range]范围内的敌人造成伤害，伤害值为当前武器面板战斗力的[mul]。", "cd": "", "effectType": "normal_hurt", "conditionType": "passive", "target": "me,random,enemy", "duration": "0.1", "mul": "1", "range": "300", "iconUrl": ""}, {"name": "rolling_hero_link", "cnName": "翻滚-减速", "father": "heroSkillLink", "index": "0", "description": "", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "me,random,enemy", "duration": "1.5", "mul": "0.7", "range": "60", "iconUrl": ""}, {"name": "沉默-使我方技能免疫3秒", "cnName": "沉默-使我方技能免疫3秒", "father": "heroSkillLink", "index": "0", "description": "", "cd": "", "effectType": "spellImmunityB", "conditionType": "active", "target": "me,range,we", "duration": "3", "mul": "", "range": "1500", "iconUrl": ""}, {"name": "moreMissile_hero_dizziness", "cnName": "击中眩晕", "father": "heroSkillLink", "index": "", "description": "击中目标使其陷入眩晕状态，持续[duration]秒。", "cd": "", "effectType": "dizziness", "conditionType": "passive", "target": "target", "duration": "2", "mul": "", "range": "", "iconUrl": ""}, {"name": "moreMissile_hero_dizziness2", "cnName": "击中眩晕", "father": "heroSkillLink", "index": "", "description": "击中目标使其陷入眩晕状态，持续[duration]秒，无视技能免疫。", "cd": "", "effectType": "dizziness", "conditionType": "passive", "target": "target", "duration": "3", "mul": "", "range": "", "iconUrl": ""}], "152outfitSkillClass": [{"name": "战争抗体", "cnName": "战争抗体", "father": "outfitSkill", "index": "1", "description": "抵挡跟踪型武器[1-mul]的伤害，包括百分比伤害。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.2", "range": "", "iconUrl": ""}, {"name": "outfit_blood", "cnName": "坏血", "father": "outfitSkill", "index": "0", "description": "使[minRange]~[range]码以内敌人的生命回复效果降低[1-mul]、头部防御降低[1-secMul]。", "cd": "", "effectType": "outfitBlood", "conditionType": "passive", "target": "me,range,enemy", "duration": "1", "mul": "0.2", "range": "700", "iconUrl": ""}, {"name": "猎手原形", "cnName": "猎手原形", "father": "outfitSkill", "index": "3", "description": "每次射击都有[effectProArr.0]的概率派生跟踪子弹。子弹伤害为当前面板单伤*射速*0.2。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "1", "range": "", "iconUrl": ""}, {"name": "悦动先锋", "cnName": "悦动先锋", "father": "outfitSkill", "index": "3", "description": "玩家握持手枪弹跳一次，会获得提高[mul-1]攻击力的状态，持续[duration]秒。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "me", "duration": "1.5", "mul": "1.9", "range": "", "iconUrl": ""}, {"name": "饥饿行踪", "cnName": "饥饿行踪", "father": "outfitSkill", "index": "3", "description": "距离目标越远，射击时产生的暴击伤害越大，大于[range]码开始加成，每隔100码增加[mul]的暴击伤害，最大增加20%。对人物技能“隐身”的暴击无效。", "cd": "", "effectType": "critExtraMul", "conditionType": "passive", "target": "target", "duration": "", "mul": "0.05", "range": "300", "iconUrl": ""}, {"name": "血脉沸腾", "cnName": "血脉沸腾", "father": "outfitSkill", "index": "3", "description": "火炮枪每次射击有[effectProArr.0]概率，使得人物技能“定点轰炸”的当前CD变为[value]秒，持续[duration]秒。", "cd": "", "effectType": "ctrlSkillCdLessThan", "conditionType": "passive", "target": "me", "duration": "2.6", "mul": "", "range": "", "iconUrl": ""}, {"name": "沃龙隐", "cnName": "沃龙隐", "father": "outfitSkill", "index": "3", "description": "当单位受到伤害、同时与攻击者距离大于[conditionRange]码时，单位将进入隐身状态，攻击力提高[mul-1]，攻击时不会打破隐身状态，隐身持续[duration]秒，攻击力加成持续6秒。", "cd": "", "effectType": "wolongHiding", "conditionType": "passive", "target": "me", "duration": "1", "mul": "1.8", "range": "", "iconUrl": ""}, {"name": "沃龙隐-攻击力加成", "cnName": "沃龙隐", "father": "outfitSkill", "index": "3", "description": "伤害加成", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "me", "duration": "8", "mul": "1.8", "range": "", "iconUrl": ""}, {"name": "鹰眼", "cnName": "鹰眼", "father": "outfitSkill", "index": "", "description": "当敌人在空中时，对其头部造成额外[mul-1]的伤害。", "cd": "", "effectType": "changeHurt_head", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.5", "range": "", "iconUrl": ""}, {"name": "遁形", "cnName": "遁形", "father": "outfitSkill", "index": "", "description": "蹲下后将技能免疫，持续[duration]秒。", "cd": "", "effectType": "spellImmunitySquat", "conditionType": "passive", "target": "me", "duration": "4", "mul": "", "range": "", "iconUrl": ""}], "162peakSkillClass": [{"name": "核爆", "cnName": "核爆", "father": "peakSkill", "index": "", "description": "释放嗜爪或隐匿之雾技能时，在鼠标处引爆炸弹伤害周围敌人，瞬秒小怪（不高于人物等级），减少精英怪、小怪（高于人物等级）25%的生命值，减少首领[mul]的生命值（首领生命值需不低于30%）。修罗模式、99级主线任务中伤害百分比减半。", "cd": "", "effectType": "nuclear_peak", "conditionType": "passive", "target": "mouse,range,enemy", "duration": "", "mul": "0.02", "range": "150", "iconUrl": ""}, {"name": "gaze_peak", "cnName": "注视", "father": "peakSkill", "index": "", "description": "在人物有暴击属性时，下蹲可增加[mul]的暴击几率。", "cd": "", "effectType": "critExtraPro", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.03", "range": "", "iconUrl": ""}, {"name": "sacrifice_peak", "cnName": "搏命", "father": "peakSkill", "index": "", "description": "每减少5%生命值，就增加[mul]的伤害，最高增加150%的伤害。", "cd": "", "effectType": "sacrifice_peak", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.03", "range": "", "iconUrl": ""}], "187yingSkillClass": [{"name": "怒蜂", "cnName": "怒蜂", "father": "heroSkill", "index": "", "description": "以下情况可召唤怒蜂无人机：击杀持枪敌人(33%的概率)、击杀普通敌人(13%概率)、我方单位被杀死(100%概率)。一关最多召唤[value]只，它的伤害为当前武器面板战斗力的[obj.dpsMul]倍。{font color='#00FF00'}升级至第14级后，无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "uavYing", "conditionType": "passive", "target": "me", "duration": "9999999999", "mul": "", "range": "", "iconUrl": ""}, {"name": "群蜂", "cnName": "群蜂", "father": "heroSkill", "index": "", "description": "如果在场怒蜂无人机的数量少于[value]个，则一次性补全到[value]个。怒蜂的伤害为当前武器面板战斗力的[obj.dpsMul]倍。", "cd": "55", "effectType": "moreUav", "conditionType": "active", "target": "me", "duration": "9999999999", "mul": "", "range": "", "iconUrl": ""}, {"name": "hurtAddYing", "cnName": "精锐之师", "father": "heroSkill", "index": "", "description": "场上每存在1个我方单位，小樱和所有怒蜂的伤害就提升[mul]（不衰减），最高提升[secMul]。{font color='#00FF00'}升级至第14级后，无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "hurtAddYing", "conditionType": "passive", "target": "me,range,we", "duration": "1", "mul": "0.05", "range": "999999", "iconUrl": ""}, {"name": "trueshotYing", "cnName": "强击领域", "father": "heroSkill", "index": "", "description": "提升所有我方单位[mul-1]的伤害（不衰减）。{font color='#00FF00'}升级至第14级后，无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "allHurtMul", "conditionType": "passive", "target": "me,range,we", "duration": "2", "mul": "1.07", "range": "99999", "iconUrl": ""}], "219loveSkillWenJieClass": [{"name": "dedicationLove", "cnName": "奉献", "father": "loveSkill", "index": "", "description": "受伤时，提升附近[range]码内队友[1-mul]的防御力，持续[duration]秒。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "me,range,we", "duration": "4", "mul": "0.85", "range": "900", "iconUrl": ""}, {"name": "resistPerLove2", "cnName": "巨伤盾三", "father": "loveSkill", "index": "", "description": "关卡中抵挡3次百分比伤害。", "cd": "", "effectType": "noMulHurt", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "defenceAddLove", "cnName": "防御力提升20%", "father": "loveSkill", "index": "", "description": "战斗中永久提升[1-mul]的防御力。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "0.8", "range": "", "iconUrl": ""}, {"name": "vehicleAddLove30", "cnName": "载具攻防", "father": "loveSkill", "index": "", "description": "战斗中提升载具[mul-1]的防御力和攻击力。", "cd": "", "effectType": "defenceAndAttack", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "1.3", "range": "", "iconUrl": ""}, {"name": "resistPerLoveAdd2", "cnName": "继续抵挡3次百分比伤害", "father": "loveSkill", "index": "", "description": "关卡中抵挡6次百分比伤害。", "cd": "", "effectType": "noMulHurt", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "nearAddLifeLove", "cnName": "靠近回血", "father": "loveSkill", "index": "", "description": "队友靠近表哥时，生命回复速度将加快[mul-1]。", "cd": "", "effectType": "lifeRateMul", "conditionType": "passive", "target": "me,range,we", "duration": "0.5", "mul": "2.5", "range": "80", "iconUrl": ""}, {"name": "mainResistPerLove", "cnName": "P1角色抵挡3次百分比伤害", "father": "loveSkill", "index": "", "description": "P1角色在关卡中可抵挡3次百分比伤害。", "cd": "", "effectType": "noMulHurt", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}], "223skillClass": [{"name": "增加血量", "cnName": "增加血量", "father": "dropEffect", "index": "0", "description": "", "cd": "", "effectType": "dropAddLife", "conditionType": "", "target": "", "duration": "", "mul": "0.3", "range": "", "iconUrl": ""}, {"name": "增加弹药", "cnName": "增加弹药", "father": "dropEffect", "index": "0", "description": "", "cd": "", "effectType": "charger", "conditionType": "", "target": "", "duration": "", "mul": "0.5", "range": "", "iconUrl": ""}, {"name": "无敌药水", "cnName": "无敌药水", "father": "dropEffect", "index": "", "description": "", "cd": "", "effectType": "invincible", "conditionType": "", "target": "", "duration": "5", "mul": "", "range": "", "iconUrl": ""}, {"name": "invinCannedDrop", "cnName": "无敌罐头", "father": "dropEffect", "index": "", "description": "", "cd": "", "effectType": "invincible", "conditionType": "", "target": "", "duration": "10", "mul": "", "range": "", "iconUrl": ""}, {"name": "hurtDrugDrop", "cnName": "增伤药剂", "father": "dropEffect", "index": "", "description": "", "cd": "", "effectType": "allHurtMul", "conditionType": "", "target": "", "duration": "7", "mul": "2", "range": "", "iconUrl": ""}, {"name": "增加移动速度", "cnName": "增加移动速度", "father": "dropEffect", "index": "0", "description": "", "cd": "", "effectType": "moveSpeed", "conditionType": "", "target": "", "duration": "5", "mul": "1.3", "range": "0", "iconUrl": ""}, {"name": "add<PERSON>ocha", "cnName": "召唤摩卡", "father": "dropEffect", "index": "0", "description": "蝙蝠。", "cd": "", "effectType": "summonedUnitsMocha", "conditionType": "active", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "zombieBottle", "cnName": "僵尸血瓶", "father": "thingsEffect", "index": "", "description": "给在场所有我方单位抹上僵尸血液，使其不会成为敌人的攻击目标。", "cd": "", "effectType": "noAiFind", "conditionType": "active", "target": "me,range,we", "duration": "999999", "mul": "", "range": "99999", "iconUrl": ""}, {"name": "glutinous", "cnName": "辣椒糯米饭", "father": "thingsEffect", "index": "", "description": "无限怒气。", "cd": "", "effectType": "glutinous", "conditionType": "active", "target": "me", "duration": "999999", "mul": "1.5", "range": "", "iconUrl": ""}, {"name": "iconGiftBox", "cnName": "圣诞铁箱", "father": "thingsEffect", "index": "", "description": "。", "cd": "", "effectType": "summonedUnits", "conditionType": "active", "target": "me", "duration": "120", "mul": "", "range": "", "iconUrl": ""}, {"name": "iconGiftBoxBuff", "cnName": "圣诞铁箱-buff", "father": "thingsEffect", "index": "", "description": "强制吸引周围[range]码以内的敌方攻击自己。", "cd": "", "effectType": "lockTargetMe", "conditionType": "passive", "target": "me,range,enemy", "duration": "1", "mul": "", "range": "99999", "iconUrl": ""}, {"name": "godHiding_things", "cnName": "无敌隐身-重生石", "father": "thingsEffect", "index": "", "description": "释放技能后，单位进入无敌隐身状态，持续[duration]秒。如果你对敌人发起攻击会打破隐身状态。", "cd": "", "effectType": "godHiding_things", "conditionType": "active", "target": "me", "duration": "3", "mul": "", "range": "", "iconUrl": ""}, {"name": "godHiding_Pet", "cnName": "无敌隐身", "father": "thingsEffect", "index": "0", "description": "释放技能后，单位进入无敌隐身状态，持续[duration]秒。如果你对敌人发起攻击会打破隐身状态。", "cd": "", "effectType": "godHidingB", "conditionType": "active", "target": "me", "duration": "3", "mul": "", "range": "", "iconUrl": ""}, {"name": "moonCake", "cnName": "增加攻击力", "father": "thingsEffect", "index": "0", "description": "使用后将增加战斗中100%的攻击力。", "cd": "", "effectType": "hurtMul", "conditionType": "active", "target": "me", "duration": "1", "mul": "2", "range": "", "iconUrl": ""}, {"name": "tangyuan", "cnName": "汤圆攻击力", "father": "thingsEffect", "index": "0", "description": "使用后将增加战斗中100%的攻击力。", "cd": "", "effectType": "hurtMul", "conditionType": "active", "target": "me", "duration": "1", "mul": "2", "range": "", "iconUrl": ""}, {"name": "nineCake", "cnName": "九层糕增加射速", "father": "thingsEffect", "index": "", "description": "使用后将增加战斗中100%的射速。", "cd": "", "effectType": "attackGapMul", "conditionType": "active", "target": "me", "duration": "1", "mul": "2", "range": "", "iconUrl": ""}, {"name": "<PERSON><PERSON><PERSON>", "cnName": "饺子无敌状态", "father": "thingsEffect", "index": "0", "description": "使用无敌。", "cd": "", "effectType": "invincible", "conditionType": "active", "target": "me", "duration": "1", "mul": "", "range": "", "iconUrl": ""}, {"name": "armsDropDouble", "cnName": "双倍黑色武器掉落", "father": "thingsEffect", "index": "", "description": "双倍黑色武器掉落", "cd": "", "effectType": "armsDrop", "conditionType": "active", "target": "me", "duration": "1", "mul": "1", "range": "", "iconUrl": ""}, {"name": "equipDropDouble", "cnName": "双倍黑色装备掉落", "father": "thingsEffect", "index": "", "description": "双倍黑色装备掉落", "cd": "", "effectType": "equipDrop", "conditionType": "active", "target": "me", "duration": "1", "mul": "1", "range": "", "iconUrl": ""}, {"name": "armsDropDoubleAndGem", "cnName": "双倍黑色武器掉落和30%宝石掉落", "father": "thingsEffect", "index": "", "description": "双倍黑色武器掉落", "cd": "", "effectType": "armsDropAndGem", "conditionType": "active", "target": "me", "duration": "1", "mul": "1", "range": "", "iconUrl": ""}, {"name": "equipDropDoubleAndEquipGem", "cnName": "双倍黑色装备掉落和30%宝石掉落", "father": "thingsEffect", "index": "", "description": "双倍黑色装备掉落", "cd": "", "effectType": "equipDropAndGem", "conditionType": "active", "target": "me", "duration": "1", "mul": "1", "range": "", "iconUrl": ""}, {"name": "highPetCard", "cnName": "神宠卡片", "father": "thingsEffect", "index": "", "description": "4倍宠物伤害", "cd": "", "effectType": "highPetCard", "conditionType": "active", "target": "me", "duration": "1", "mul": "", "range": "", "iconUrl": ""}, {"name": "highVehicleCard", "cnName": "神车卡片", "father": "thingsEffect", "index": "", "description": "4倍载具伤害", "cd": "", "effectType": "highVehicleCard", "conditionType": "active", "target": "me", "duration": "1", "mul": "", "range": "", "iconUrl": ""}, {"name": "highCardState", "cnName": "神宠卡片、神车卡片状态", "father": "thingsEffect", "index": "", "description": "4倍伤害", "cd": "", "effectType": "otherHurtMul", "conditionType": "passive", "target": "me", "duration": "1", "mul": "4", "range": "", "iconUrl": ""}, {"name": "highPartnerCard", "cnName": "神队卡片", "father": "thingsEffect", "index": "", "description": "1倍持枪队友伤害", "cd": "", "effectType": "highPartnerCard", "conditionType": "active", "target": "me", "duration": "1", "mul": "", "range": "", "iconUrl": ""}, {"name": "highPartnerState", "cnName": "神队卡片状态", "father": "thingsEffect", "index": "", "description": "1倍伤害", "cd": "", "effectType": "otherHurtMul", "conditionType": "passive", "target": "me", "duration": "1", "mul": "2", "range": "", "iconUrl": ""}, {"name": "electromagnet", "cnName": "磁力场", "father": "thingsEffect", "index": "0", "description": "向外释放磁力场，使700码的范围内的所有敌方子弹偏离轨道。", "cd": "", "effectType": "magneticB", "conditionType": "active", "target": "me", "duration": "1", "mul": "", "range": "200", "iconUrl": ""}, {"name": "superSpreadCard", "cnName": "超级散射卡", "father": "thingsEffect", "index": "0", "description": "", "cd": "", "effectType": "setBulletNum", "conditionType": "active", "target": "me", "duration": "1", "mul": "5", "range": "", "iconUrl": ""}, {"name": "skeletonCard", "cnName": "骷髅卡", "father": "thingsEffect", "index": "0", "description": "", "cd": "", "effectType": "no", "conditionType": "active", "target": "me", "duration": "1", "mul": "", "range": "", "iconUrl": ""}, {"name": "skeletonCard_link", "cnName": "骷髅卡扣血", "father": "thingsEffect", "index": "", "description": "使用后在关卡中（任务、修罗模式无效）让敌人每秒损失2%的生命值（首领每秒损失1%，生命低于15%时不再损失）。", "cd": "", "effectType": "skeletonCard", "conditionType": "active", "target": "me,range,enemy", "duration": "1", "mul": "0.02", "range": "99999", "iconUrl": ""}, {"name": "pumpkinHead", "cnName": "南瓜头技能", "father": "fashion", "index": "", "description": "红色及红色以下的武器，伤害提升10倍。主线、争霸、修罗决斗无效。", "cd": "", "effectType": "lessRedArms", "conditionType": "passive", "target": "me", "duration": "", "mul": "10", "range": "", "iconUrl": ""}, {"name": "wolfFashionSkill", "cnName": "狼震", "father": "fashion", "index": "", "description": "同时触发人物技能狂暴、电离折射时，对周围1000码内的敌人发起狼震攻击，造成相当于当前武器面板伤害*射速*10的伤害。", "cd": "", "effectType": "normal_hurt", "conditionType": "passive", "target": "me,range,enemy", "duration": "", "mul": "10", "range": "1000", "iconUrl": ""}, {"name": "goldFalcon", "cnName": "金翼", "father": "fashion", "index": "", "description": "进入关卡将长出黄金翅膀，让角色一直处于飞行状态。主线任务、首领工厂的困难模式下无效。未穿该时装时，可用“使用外观”来让角色获得该技能效果。可使用“I”键来切换陆空状态。", "cd": "", "effectType": "goldFalcon", "conditionType": "passive", "target": "me", "duration": "9999999", "mul": "", "range": "", "iconUrl": ""}, {"name": "dragonHeadSkill", "cnName": "龙翅", "father": "fashion", "index": "", "description": "进入关卡将长出龙翅，让角色一直处于飞行状态。主线任务、争霸下无效。可使用“I”键来切换陆空状态。", "cd": "", "effectType": "dragonHeadSkill", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "zombieMask", "cnName": "伪装", "father": "fashion", "index": "", "description": "伪装成僵尸，使敌人永远不会攻击你。任务中无效。", "cd": "", "effectType": "noAiFind", "conditionType": "passive", "target": "me", "duration": "9999999", "mul": "", "range": "", "iconUrl": ""}, {"name": "crazy_sanji", "cnName": "恶魔风脚", "father": "fashion", "index": "", "description": "载具使用完后，自身燃起烈火，攻击力增加100%，射击速度增加100%，不消耗子弹（非修罗模式），持续5秒。", "cd": "", "effectType": "shootSpeedAndHurt", "conditionType": "passive", "target": "me", "duration": "5", "mul": "2", "range": "", "iconUrl": ""}, {"name": "xiaoBoShoot", "cnName": "飞雷神", "father": "fashion", "index": "", "description": "挥舞沃龙牌棍棒，释放出分身，对前方的敌人造成最大生命值12%的伤害。修罗模式、99级主线任务中效果降低。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "xiaoMingShoot", "cnName": "鸣人", "father": "fashion", "index": "", "description": "挥舞屠夫砍击前方所有敌人，对普通单位造成瞬秒伤害，对首领造成最大生命值15%的伤害。修罗模式、99级主线任务下效果降低。", "cd": "", "effectType": "bullet_xiaoMing", "conditionType": "passive", "target": "me", "duration": "1.2", "mul": "0.15", "range": "", "iconUrl": ""}, {"name": "瞬秒-小怪", "cnName": "瞬秒", "father": "fashion", "index": "1", "description": "", "cd": "", "effectType": "seckillNormalEnemy", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "xiaoAiShoot", "cnName": "小炎戒", "father": "fashion", "index": "", "description": "将火焰灌输进子弹，高温子弹使敌人燃烧，每秒造成最大生命值1.1%的灼烧伤害。修罗模式下效果降低。", "cd": "", "effectType": "poison_xiaoAi", "conditionType": "passive", "target": "target", "duration": "2", "mul": "0.011", "range": "", "iconUrl": ""}, {"name": "shotgunBladeHero", "cnName": "英雄跳斩-降低目标攻击力", "father": "fashion", "index": "", "description": "减少被击中目标[1-mul]的攻击力。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "target", "duration": "5", "mul": "0.3", "range": "", "iconUrl": ""}, {"name": "chinaCaptainSkill", "cnName": "队长之魄", "father": "fashion", "index": "", "description": "震慑1500码内的敌人，使他们所有主动技能冷却时间延长30%。", "cd": "", "effectType": "cdMul", "conditionType": "passive", "target": "me,range,enemy", "duration": "2", "mul": "1.3", "range": "1500", "iconUrl": ""}, {"name": "armyCommanderSkill", "cnName": "鼓舞士气", "father": "fashion", "index": "", "description": "使周围队友（除了P1角色）的防御力提升30%、百分比伤害降低30%。", "cd": "", "effectType": "underHurtMulAndMul", "conditionType": "passive", "target": "me,range,we", "duration": "2", "mul": "0.7", "range": "99999", "iconUrl": ""}, {"name": "snowShadowSkill", "cnName": "怒目", "father": "fashion", "index": "", "description": "保持移动速度不降低，受到伤害时能保持准星；初始拥有[value]%的怒气，怒气回复速度增加至原来的[mul]；使用月刺将拥有较大攻击范围。", "cd": "", "effectType": "snowShadowSkill", "conditionType": "passive", "target": "me", "duration": "999999", "mul": "1.5", "range": "", "iconUrl": ""}, {"name": "foxLingSkill", "cnName": "流萤扇", "father": "fashion", "index": "", "description": "初始拥有[value]%的怒气，怒气回复速度增加至原来的[mul]；使用剑齿镖时将投出跟踪流萤扇。", "cd": "", "effectType": "bladeSkill", "conditionType": "passive", "target": "me", "duration": "999999", "mul": "2.3", "range": "", "iconUrl": ""}, {"name": "bladeSkill", "cnName": "盛怒", "father": "fashion", "index": "", "description": "初始拥有[value]%的怒气，怒气回复速度增加至原来的[mul]；使用屠夫时将拥有超大攻击范围。", "cd": "", "effectType": "bladeSkill", "conditionType": "passive", "target": "me", "duration": "999999", "mul": "2.3", "range": "", "iconUrl": ""}, {"name": "hundredGhostsSkill", "cnName": "鬼诀", "father": "fashion", "index": "", "description": "使用凯撒有专属动作；在装备8级镭射穿梭器时，还会在出招前瞬移到鼠标点位置（可在“系统>其他”中设置成双击瞬移）。", "cd": "", "effectType": "no", "conditionType": "passive", "target": "me", "duration": "999999", "mul": "", "range": "", "iconUrl": ""}, {"name": "cyanArmySkill", "cnName": "无敌之怒", "father": "fashion", "index": "", "description": "攻击无敌怪物时，你的伤害会提高400%，持续3秒。", "cd": "", "effectType": "otherHurtMul", "conditionType": "passive", "target": "me", "duration": "3", "mul": "4", "range": "", "iconUrl": ""}, {"name": "生化锁定", "cnName": "生化锁定", "father": "fashion", "index": "0", "description": "对首领造成额外30%的伤害。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.30", "range": "", "iconUrl": ""}, {"name": "highDrill", "cnName": "挖掘者-动能推撞", "father": "vehicleNormal", "index": "", "description": "", "cd": "", "effectType": "moveSpeedAllStop", "conditionType": "passive", "target": "target", "duration": "1", "mul": "0", "range": "", "iconUrl": ""}, {"name": "superHighDrill", "cnName": "挖掘者-超能推撞", "father": "vehicleNormal", "index": "", "description": "", "cd": "", "effectType": "moveSpeedAllStop", "conditionType": "passive", "target": "target", "duration": "3", "mul": "0", "range": "", "iconUrl": ""}, {"name": "highDrillHit", "cnName": "挖掘者-动能推撞-推", "father": "vehicleNormal", "index": "", "description": "", "cd": "", "effectType": "highDrillHit", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "unendBuff", "cnName": "虚天塔Buff", "father": "forest", "index": "", "description": "", "cd": "", "effectType": "unendBuff", "conditionType": "passive", "target": "me", "duration": "9999999999", "mul": "", "range": "", "iconUrl": ""}, {"name": "addChargerMax2", "cnName": "2倍携弹量", "father": "forest", "index": "", "description": "", "cd": "", "effectType": "addChargerMaxMul", "conditionType": "passive", "target": "me", "duration": "", "mul": "2", "range": "", "iconUrl": ""}, {"name": "extendCd", "cnName": "技能衰减", "father": "forest", "index": "", "description": "使周围所有敌人的技能冷却时间延长1倍。", "cd": "", "effectType": "cdMulAndMinTrigger", "conditionType": "passive", "target": "me,range,enemy", "duration": "2", "mul": "1.5", "range": "99999", "iconUrl": ""}, {"name": "竞技场-伤害加倍", "cnName": "竞技场-伤害加倍", "father": "forest", "index": "", "description": "", "cd": "", "effectType": "arenaHurtAdd", "conditionType": "passive", "target": "target", "duration": "9999999", "mul": "2", "range": "", "iconUrl": ""}, {"name": "击中减速", "cnName": "击中减速", "father": "forest", "index": "0", "description": "", "cd": "", "effectType": "moveSpeed", "conditionType": "passive", "target": "target", "duration": "2", "mul": "0.5", "range": "0", "iconUrl": ""}, {"name": "减速光环", "cnName": "减速光环", "father": "forest", "index": "0", "description": "", "cd": "", "effectType": "moveSpeed", "conditionType": "passive", "target": "me,range,enemy", "duration": "1", "mul": "0.3", "range": "300", "iconUrl": ""}, {"name": "主动加血", "cnName": "主动加血", "father": "forest", "index": "0", "description": "", "cd": "", "effectType": "life", "conditionType": "", "target": "", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "反弹伤害", "cnName": "反弹伤害", "father": "forest", "index": "0", "description": "", "cd": "", "effectType": "backHurt", "conditionType": "passive", "target": "target", "duration": "3", "mul": "0.2", "range": "", "iconUrl": ""}, {"name": "火花面具", "cnName": "花火面具", "father": "forest", "index": "0", "description": "带上面具，你就是花火！", "cd": "", "effectType": "gmMask", "conditionType": "passive", "target": "me", "duration": "9999999", "mul": "", "range": "", "iconUrl": ""}, {"name": "沃龙面具", "cnName": "沃龙面具", "father": "forest", "index": "0", "description": "带上面具，你就是沃龙！", "cd": "", "effectType": "gmMask", "conditionType": "passive", "target": "me", "duration": "9999999", "mul": "", "range": "", "iconUrl": ""}, {"name": "丛安", "cnName": "丛安面具", "father": "forest", "index": "0", "description": "带上面具，你就是丛安！", "cd": "", "effectType": "gmMask", "conditionType": "passive", "target": "me", "duration": "9999999", "mul": "", "range": "", "iconUrl": ""}, {"name": "rollingFast", "cnName": "翻滚", "father": "task", "index": "", "description": "双击移动按键可向前翻滚[value]码，期间不受攻击、子弹碰撞，最短触发间隔[minTriggerT]秒。", "cd": "", "effectType": "hero<PERSON><PERSON>ing", "conditionType": "passive", "target": "me", "duration": "0.5", "mul": "0", "range": "", "iconUrl": "SkillIcon/rolling_hero"}, {"name": "speedUpTask", "cnName": "加速", "father": "task", "index": "", "description": "技能释放后，提升自身移动速度。", "cd": "20", "effectType": "moveSpeed", "conditionType": "active", "target": "me", "duration": "5", "mul": "", "range": "", "iconUrl": ""}, {"name": "超级散射", "cnName": "超级散射", "father": "task", "index": "", "description": "增加[value]倍散射子弹。", "cd": "", "effectType": "setBulletNum", "conditionType": "passive", "target": "me", "duration": "999999", "mul": "5", "range": "", "iconUrl": ""}, {"name": "无限馈赠", "cnName": "无限馈赠", "father": "task", "index": "", "description": "", "cd": "", "effectType": "heroSkillFullCd", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "极限射速", "cnName": "极限射速", "father": "task", "index": "", "description": "增加[mul]倍的射击速度。", "cd": "", "effectType": "maxSpeedTask", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "5", "range": "", "iconUrl": ""}, {"name": "战争狂人脱下头盔", "cnName": "战争狂人脱下头盔", "father": "task", "index": "", "description": "", "cd": "", "effectType": "mad<PERSON><PERSON><PERSON>", "conditionType": "active", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "丛林特种兵技能", "cnName": "丛林特种兵技能", "father": "task", "index": "", "description": "", "cd": "", "effectType": "betHit", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "瞬秒所有", "cnName": "瞬秒", "father": "task", "index": "", "description": "", "cd": "", "effectType": "kill", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "attackNoDodge", "cnName": "攻击无视闪避", "father": "task", "index": "", "description": "", "cd": "", "effectType": "attackNoDodge", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "", "range": "", "iconUrl": ""}, {"name": "findHide", "cnName": "红外眼", "father": "task", "index": "", "description": "可看见隐身敌人。", "cd": "", "effectType": "findHideB", "conditionType": "passive", "target": "me", "duration": "999999", "mul": "", "range": "", "iconUrl": ""}, {"name": "sniper<PERSON><PERSON><PERSON><PERSON>", "cnName": "狙击之王任务P1角色状态", "father": "task", "index": "", "description": "", "cd": "", "effectType": "sniper<PERSON><PERSON><PERSON><PERSON>", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "", "range": "", "iconUrl": ""}, {"name": "bulletRainBallHit", "cnName": "子弹碰撞", "father": "task", "index": "", "description": "", "cd": "", "effectType": "bulletRainBallHit", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "flySkyBatBuff", "cnName": "蝙蝠状态", "father": "task", "index": "", "description": "", "cd": "", "effectType": "no", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "", "range": "", "iconUrl": ""}], "232nightmareSkillClass": [{"name": "rifleSensitive", "cnName": "步枪敏感", "father": "nightmare", "index": "", "description": "降低[1-mul]受到的伤害（除步枪外）。", "cd": "", "effectType": "armsSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "sniperSensitive", "cnName": "狙击敏感", "father": "nightmare", "index": "", "description": "降低[1-mul]受到的伤害（除狙击枪外）。", "cd": "", "effectType": "armsSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "shotgunSensitive", "cnName": "散弹敏感", "father": "nightmare", "index": "", "description": "降低[1-mul]受到的伤害（除散弹枪外）。", "cd": "", "effectType": "armsSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "pistolSensitive", "cnName": "手枪敏感", "father": "nightmare", "index": "", "description": "降低[1-mul]受到的伤害（除手枪外）。", "cd": "", "effectType": "armsSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "rocketSensitive", "cnName": "火炮敏感", "father": "nightmare", "index": "", "description": "降低[1-mul]受到的伤害（除火炮外）。", "cd": "", "effectType": "armsSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "crossbowSensitive", "cnName": "弩敏感", "father": "nightmare", "index": "", "description": "降低[1-mul]受到的伤害（除弩外）。", "cd": "", "effectType": "armsSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "flamerSensitive", "cnName": "喷火器敏感", "father": "nightmare", "index": "", "description": "受到喷火器的伤害提升[secMul-1]，其他伤害降低[1-mul]。", "cd": "", "effectType": "armsSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "laserSensitive", "cnName": "激光枪敏感", "father": "nightmare", "index": "", "description": "降低[1-mul]受到的伤害（除激光枪外）。", "cd": "", "effectType": "armsSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "otherSensitive", "cnName": "其他敏感", "father": "nightmare", "index": "", "description": "受到榴弹炮、波动枪、闪电枪、切割枪、气象枪的伤害提升[secMul-1]，其他伤害降低[1-mul]。", "cd": "", "effectType": "otherSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.15", "range": "", "iconUrl": ""}, {"name": "weaponSensitive", "cnName": "副手敏感", "father": "nightmare", "index": "", "description": "降低[1-mul]受到的伤害（除副手外）。", "cd": "", "effectType": "weaponSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "0", "range": "", "iconUrl": ""}, {"name": "vehicleSensitive", "cnName": "载具敏感", "father": "nightmare", "index": "", "description": "只受载具的伤害。", "cd": "", "effectType": "vehicleSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "0", "range": "", "iconUrl": ""}, {"name": "petSensitive", "cnName": "尸宠敏感", "father": "nightmare", "index": "", "description": "只受尸宠的伤害。", "cd": "", "effectType": "petSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "0", "range": "", "iconUrl": ""}, {"name": "redArmsSensitive", "cnName": "红武敏感", "father": "nightmare", "index": "", "description": "只受红色武器的伤害。", "cd": "", "effectType": "redArmsSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "handSensitive", "cnName": "徒手敏感", "father": "nightmare", "index": "", "description": "只受徒手攻击的伤害。", "cd": "", "effectType": "handSensitive", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}], "246partsSkillClass": [{"name": "猎击", "cnName": "猎击", "father": "partsSkill", "index": "", "description": "自身生命值大于90%时，对敌人造成[mul]的伤害。60%的概率无视技能免疫。", "cd": "", "effectType": "changeHurtNoCondition", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.3", "range": "", "iconUrl": ""}, {"name": "酸性腐蚀", "cnName": "酸性腐蚀", "father": "partsSkill", "index": "", "description": "腐蚀机械体敌人的外壳，降低其[mul-1]的防御力。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "target", "duration": "1", "mul": "1.05", "range": "", "iconUrl": ""}, {"name": "爆裂弹", "cnName": "爆裂", "father": "partsSkill", "index": "", "description": "持续射击[duration]秒，将有[effectProArr.0]概率产生[duration]秒的[value]倍散射效果。", "cd": "", "effectType": "setBulletNum", "conditionType": "passive", "target": "me", "duration": "1", "mul": "3", "range": "", "iconUrl": ""}, {"name": "月饼子弹", "cnName": "吃月饼", "father": "partsSkill", "index": "", "description": "把子弹型的子弹外观变为月饼。在有月亮的地图中，对敌人造成额外[mul-1]的伤害。", "cd": "", "effectType": "changeHurtNoCondition", "conditionType": "passive", "target": "me", "duration": "", "mul": "1.2", "range": "", "iconUrl": ""}, {"name": "修罗弹夹", "cnName": "弹药补充", "father": "partsSkill", "index": "", "description": "在修罗（独战、冷门、决斗）模式中，每次射击都有[effectProArr.0]的概率补充[value]颗弹药。", "cd": "", "effectType": "addCapacity", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}], "247petSkillClass": [{"name": "狂战尸-旋风刀", "cnName": "旋风刀", "father": "petBodySkill", "index": "0", "description": "狂战尸旋转手中的狂刃，并向前冲刺，持续对经过的敌人造成伤害！", "cd": "33", "effectType": "no", "conditionType": "active", "target": "me", "duration": "0.1", "mul": "", "range": "", "iconUrl": ""}, {"name": "狂战尸-震地", "cnName": "震地", "father": "petBodySkill", "index": "0", "description": "狂战尸大吼一声，集中全身力量将狂刃插入地面，对周围的敌人造成伤害，同时降低目标40%的移动速度，持续4秒。", "cd": "42", "effectType": "no", "conditionType": "active", "target": "me", "duration": "0.1", "mul": "", "range": "", "iconUrl": ""}, {"name": "狂战尸-狂刃追踪", "cnName": "狂刃追踪", "father": "petBodySkill", "index": "0", "description": "狂战尸向空中投掷具有跟踪能力的旋转狂刃，旋转狂刃将对它所碰到的敌人造成伤害！", "cd": "56", "effectType": "no", "conditionType": "active", "target": "me", "duration": "0.1", "mul": "", "range": "", "iconUrl": ""}, {"name": "远视", "cnName": "远视", "father": "petBodySkill", "index": "0", "description": "距离目标越远伤害越大，大于[range]码开始加成，每隔100码增加[mul]的伤害，最大增加100%。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "target", "duration": "", "mul": "0.05", "range": "100", "iconUrl": "SkillIcon/hyperopia_hero"}, {"name": "helmet_PetZombieFootball", "cnName": "补给头盔", "father": "petBodySkill", "index": "0", "description": "橄榄僵尸每次受到伤害，都能够补给一定的生命值，补给量为伤害值的[mul]。", "cd": "", "effectType": "life", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.33", "range": "", "iconUrl": ""}, {"name": "橄榄僵尸-弹性世界", "cnName": "弹性世界", "father": "petBodySkill", "index": "0", "description": "橄榄僵尸向外投掷出8个弹性极强的橄榄球，橄榄球击中敌人后使目标减速同时造成伤害，每颗橄榄球在2秒后将消失。", "cd": "96", "effectType": "no", "conditionType": "active", "target": "me", "duration": "0.1", "mul": "", "range": "", "iconUrl": ""}, {"name": "全域圣光", "cnName": "全域圣光", "father": "petBodySkill", "index": "0", "description": "单位技能释放后，回复所有友方单位70%的生命值。", "cd": "115", "effectType": "life", "conditionType": "active", "target": "me,range,we", "duration": "", "mul": "0.7", "range": "9999", "iconUrl": "SkillIcon/globalLight_hero"}, {"name": "sacrifice_PetIronChief", "cnName": "牺牲", "father": "petBodySkill", "index": "0", "description": "每次受到伤害都能增加[range]范围内我方单位的生命值，增加值为伤害值的[mul]。", "cd": "", "effectType": "sacrifice", "conditionType": "passive", "target": "me,range,we", "duration": "", "mul": "0.07", "range": "450", "iconUrl": "SkillIcon/sacrifice"}, {"name": "defenceAuras_PetIronChief", "cnName": "防御光环", "father": "petBodySkill", "index": "0", "description": "为周围[range]码以内的我方单位增加[1-mul]的防御力。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "me,range,we", "duration": "2", "mul": "0.93", "range": "500", "iconUrl": "SkillIcon/defenceAuras"}, {"name": "godHand_PetIronChief", "cnName": "上帝的护佑", "father": "petBodySkill", "index": "0", "description": "在受到必亡的伤害时，你将接受上帝的护佑，进入隐身无敌模式，持续[duration]秒，同时剩余1点生命值。技能触发间隔不小于[minTriggerT]秒。", "cd": "", "effectType": "godHand", "conditionType": "passive", "target": "me", "duration": "3", "mul": "", "range": "", "iconUrl": "SkillIcon/godHand"}, {"name": "无尽轰炸", "cnName": "无尽轰炸", "father": "petBodySkill", "index": "0", "description": "爆骷在5秒之内向外释放出50颗跟踪弹，每颗跟踪弹的伤害为爆骷当前战斗力的[mul]。", "cd": "150", "effectType": "bullet", "conditionType": "active", "target": "me", "duration": "", "mul": "0.6", "range": "", "iconUrl": "SkillIcon/endlessBombing_skull"}, {"name": "current_skull", "cnName": "聚能电流", "father": "petBodySkill", "index": "0", "description": "爆骷每[duration]秒向外释放一次闪电，击中[range]范围内的最近目标，伤害值为爆骷当前战斗力的[mul]。", "cd": "", "effectType": "current", "conditionType": "passive", "target": "me,near,enemy", "duration": "39", "mul": "9", "range": "350", "iconUrl": "SkillIcon/current"}, {"name": "degradation_PetBoomSkull", "cnName": "退化光环", "father": "petBodySkill", "index": "0", "description": "降低周围[range]码以内敌方单位的[mul-1]的防御力。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "me,range,enemy", "duration": "2", "mul": "1.07", "range": "400", "iconUrl": "SkillIcon/degradation"}, {"name": "charged_PetLake", "cnName": "充能", "father": "petBodySkill", "index": "0", "description": "当P1角色或者自身血量少于50%时候，瞬移到P1角色身边，使周围[range]码的我方单位受到伤害降低[1-mul]、移动速度增加[value]倍，持续[duration]秒。", "cd": "90", "effectType": "charged_PetLake", "conditionType": "active", "target": "me,range,we", "duration": "5", "mul": "0.1", "range": "400", "iconUrl": ""}, {"name": "lightBall_PetLake", "cnName": "辐射光球", "father": "petBodySkill", "index": "0", "description": "向前方发射缓慢移动的光球，对敌人造成持续伤害，受到伤害的敌人移动速度降低90%，同时使其无法释放技能。", "cd": "90", "effectType": "no", "conditionType": "active", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "static_PetLake", "cnName": "静电过载", "father": "petBodySkill", "index": "0", "description": "当周围有敌方单位时候，向P1角色方向瞬移，并使原来周围[range]码内的敌人麻痹[duration]秒， 并回复自身和P1角色[mul]的血量。", "cd": "45", "effectType": "static_PetLake", "conditionType": "active", "target": "me,range,enemy", "duration": "2", "mul": "0.01", "range": "300", "iconUrl": ""}, {"name": "agile_PetLake", "cnName": "敏捷光环", "father": "petBodySkill", "index": "0", "description": "提升周围[range]码以内的我方射击单位[mul-1]的射击速度。", "cd": "", "effectType": "attackGapMul", "conditionType": "passive", "target": "me,range,we", "duration": "2", "mul": "1.07", "range": "500", "iconUrl": ""}, {"name": "lightBall_PetLake_slow", "cnName": "减速", "father": "petBodySkill", "index": "0", "description": "击中目标后降低其[1-mul]的移动速度，持续[duration]秒。", "cd": "", "effectType": "moveSpeed", "conditionType": "passive", "target": "target", "duration": "2", "mul": "0.1", "range": "", "iconUrl": ""}, {"name": "灼热视线-眩晕", "cnName": "眩晕", "father": "petBodySkill", "index": "9", "description": "", "cd": "", "effectType": "dizziness", "conditionType": "passive", "target": "target", "duration": "0.3", "mul": "", "range": "", "iconUrl": ""}, {"name": "laser_PetFightWolf_extra", "cnName": "灼热视线-失血", "father": "petBodySkill", "index": "0", "description": "", "cd": "", "effectType": "poison", "conditionType": "passive", "target": "target", "duration": "5", "mul": "0.005", "range": "", "iconUrl": ""}, {"name": "闪烁-目标点爆炸", "cnName": "闪烁-目标点爆炸", "father": "petBodySkill", "index": "0", "description": "", "cd": "", "effectType": "bullet", "conditionType": "active", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "selfBurn_pet", "cnName": "自燃", "father": "petBodySkill", "index": "0", "description": "释放技能后，单位全身燃起熊熊烈火，对附近[range]范围内的敌人造成伤害。", "cd": "", "effectType": "normal_hurt", "conditionType": "passive", "target": "me,range,enemy", "duration": "1", "mul": "0.2", "range": "150", "iconUrl": ""}, {"name": "狂暴", "cnName": "狂暴", "father": "petSkill", "index": "0", "description": "释放技能后，本身若为射击单位，则增加100%的射击速度；若为非射击单位，则提升100%的移动速度、增加30%的攻击力。持续5秒。", "cd": "100", "effectType": "crazy", "conditionType": "active", "target": "me", "duration": "5", "mul": "2", "range": "", "iconUrl": "SkillIcon/crazy_hero"}, {"name": "沉默", "cnName": "沉默", "father": "petSkill", "index": "0", "description": "使[range]码以内的所有敌方单位无法释放技能，持续[duration]秒，同时清除目标身上的隐身、狂暴、嗜爪、电离折射、电离反转的状态。", "cd": "60", "effectType": "silenceBAndClearState", "conditionType": "active", "target": "me,range,enemy", "duration": "5", "mul": "", "range": "400", "iconUrl": "SkillIcon/silence_hero"}, {"name": "群体圣光", "cnName": "群体圣光", "father": "petSkill", "index": "0", "description": "单位技能释放后，回复单位周围[range]码以内的所有友方单位30%的生命值，每个单位都有[obj.pro]的概率直接回满生命值。", "cd": "120", "effectType": "life", "conditionType": "active", "target": "me,range,we", "duration": "", "mul": "0.3", "range": "300", "iconUrl": "SkillIcon/groupLight_hero"}, {"name": "反击", "cnName": "反击", "father": "petSkill", "index": "0", "description": "释放技能后，单位开启[mul]的吸血状态，持续[duration]秒。", "cd": "100", "effectType": "suckBlood", "conditionType": "active", "target": "me", "duration": "8", "mul": "0.3", "range": "", "iconUrl": "SkillIcon/tenacious_hero"}, {"name": "电离折射", "cnName": "电离折射", "father": "petSkill", "index": "0", "description": "释放技能后，单位将200%的伤害反弹给敌人，自身不受到伤害。持续5秒。", "cd": "90", "effectType": "noUnderHurtB", "conditionType": "active", "target": "me", "duration": "5", "mul": "", "range": "", "iconUrl": "SkillIcon/feedback_hero"}, {"name": "馈赠", "cnName": "馈赠", "father": "petSkill", "index": "0", "description": "每次单位释放完主动技能，就有[effectProArr.0]的几率大幅度回复该主动技能的冷却时间。", "cd": "", "effectType": "skillFullCd", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": "SkillIcon/skillGift_hero"}, {"name": "pioneer_hero", "cnName": "先锋盾", "father": "petSkill", "index": "0", "description": "单位生命值大于[obj.per]时，受到的伤害减少[1-mul]。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.7", "range": "", "iconUrl": "SkillIcon/pioneer_hero"}, {"name": "电离反转", "cnName": "电离反转", "father": "petSkill", "index": "0", "description": "使[range]码范围内的友方单位进入电离反转状态，拥有该状态的单位所受到伤害都会转化为自己的生命值。持续6秒。", "cd": "90", "effectType": "reverseHurt", "conditionType": "active", "target": "me,range,we", "duration": "6", "mul": "", "range": "", "iconUrl": "SkillIcon/groupRever<PERSON><PERSON><PERSON>_hero"}, {"name": "毒雾", "cnName": "毒雾", "father": "petSkill", "index": "0", "description": "在单位周围释放毒雾，感染[range]码以内的敌人，使他们降低[1-mul]移动速度并且每秒减少0.5%生命值，持续10秒。", "cd": "100", "effectType": "moveSpeed", "conditionType": "active", "target": "mouse,range,enemy", "duration": "10", "mul": "0.3", "range": "200", "iconUrl": "SkillIcon/poisonousFog_hero"}, {"name": "定点轰炸", "cnName": "定点轰炸", "father": "petSkill", "index": "0", "description": "在鼠标位置引爆一颗炸弹，对周围100码内的所有敌人造成伤害，伤害值为当前武器实际战斗力值的2倍。并且有[effectProArr.1]的几率释放2~4次。", "cd": "140", "effectType": "bullet", "conditionType": "active", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": "SkillIcon/pointBoom_hero"}, {"name": "欺凌", "cnName": "欺凌", "father": "petSkill", "index": "0", "description": "攻击生命低于[obj.per]的敌人，会对它造成[mul]的伤害。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.5", "range": "", "iconUrl": "SkillIcon/bullying_hero"}, {"name": "flash_pet", "cnName": "闪烁", "father": "petSkill", "index": "0", "description": "瞬间移动目标单位的位置，自身隐身无敌3秒，同时在目标位置引爆炸弹，对周围100码的敌人造成相当于施法者战斗力[mul]的伤害。", "cd": "30", "effectType": "teleport", "conditionType": "active", "target": "me", "duration": "", "mul": "2", "range": "", "iconUrl": ""}, {"name": "gngerFire_pet", "cnName": "怒火", "father": "petSkill", "index": "0", "description": "单位受到攻击都能使自己燃起怒火，每1秒对附近150码内的所有敌人造成伤害，伤害值为总战斗力的[mul]，持续[duration]秒。", "cd": "", "effectType": "no", "conditionType": "passive", "target": "me", "duration": "5", "mul": "0.2", "range": "", "iconUrl": "SkillIcon/selfBurn_hero"}, {"name": "paralysis_pet", "cnName": "闪电麻痹", "father": "petSkill", "index": "0", "description": "从单位背后发出3颗闪电球，击中目标后使其受到伤害并且无法移动5秒。", "cd": "70", "effectType": "bullet", "conditionType": "active", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "imploding_pet", "cnName": "爆石", "father": "petSkill", "index": "0", "description": "从单位背后爆发出5颗石头，每颗伤害为单位战斗力的2倍，并且有几率释放多次。", "cd": "75", "effectType": "bullet", "conditionType": "active", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "strong_pet", "cnName": "顽强", "father": "petSkill", "index": "0", "description": "单位生命值小于[obj.per]时，受到的伤害降低[1-mul]。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.40", "range": "", "iconUrl": ""}, {"name": "trueshot_pet", "cnName": "强击光环", "father": "petSkill", "index": "0", "description": "提升周围[range]码以内的我方单位[mul-1]的攻击力。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "me,range,we", "duration": "2", "mul": "1.07", "range": "300", "iconUrl": ""}, {"name": "recoveryHalo_pet", "cnName": "复原光环", "father": "petSkill", "index": "0", "description": "为周围[range]码以内的我方单位增加生命回复效果，每秒回复目标自身[mul]的生命值。", "cd": "", "effectType": "lifeRate", "conditionType": "passive", "target": "me,range,we", "duration": "2", "mul": "0.004", "range": "200", "iconUrl": ""}, {"name": "disabledHalo_pet", "cnName": "致残光环", "father": "petSkill", "index": "0", "description": "降低周围[range]码以内的敌方单位[1-mul]的攻击力。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "me,range,enemy", "duration": "2", "mul": "0.96", "range": "400", "iconUrl": ""}, {"name": "slowMoveHalo_pet", "cnName": "减速光环", "father": "petSkill", "index": "0", "description": "降低周围[range]码以内的敌方单位[1-mul]的移动速度。", "cd": "", "effectType": "moveSpeed", "conditionType": "passive", "target": "me,range,enemy", "duration": "2", "mul": "0.75", "range": "250", "iconUrl": ""}], "263unionSkillClass": [{"name": "人类克星", "cnName": "人类克星", "father": "unionSkill", "index": "0", "description": "对人类造成额外[mul-1]的伤害。", "cd": "", "effectType": "changeHurtNoCondition", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.1", "range": "", "iconUrl": ""}, {"name": "恶魔猎手", "cnName": "恶魔猎手", "father": "unionSkill", "index": "0", "description": "对首领造成的伤害提高[mul-1]。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.10", "range": "", "iconUrl": ""}, {"name": "恶魔屠刀", "cnName": "恶魔屠刀", "father": "unionSkill", "index": "0", "description": "对首领造成的伤害提高[mul-1]。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.20", "range": "", "iconUrl": ""}, {"name": "<PERSON><PERSON><PERSON>", "cnName": "上帝之眼", "father": "unionSkill", "index": "", "description": "同时触发人物技能组合：电离折射、定点轰炸（或者电离折射、群体隐身，或者隐匿之雾、群体隐身），技能要求最低等级为9级，将启动二元化学激光，原本无毒的化学激光交叉碰撞后，迅速产生光化学毒素，伤害范围内的人并破坏他们10%的防御力，持续20秒。", "cd": "", "effectType": "bullet_god<PERSON><PERSON>", "conditionType": "passive", "target": "me", "duration": "0.2", "mul": "", "range": "600", "iconUrl": ""}, {"name": "god<PERSON>ace", "cnName": "上帝之杖", "father": "unionSkill", "index": "", "description": "同时触发人物技能组合：反击、狂暴、金刚钻（或者反击、毒雾、真空，或者反转术、全域圣光、金刚钻），技能要求最低等级为9级，之后将引导太空中的天基动能卫星，向地面发射巨大的金属柱，形成人造陨石轰击地面，造成堪比核弹的灾难性打击，冲击波能让敌人降低20%防御力，持续20秒。", "cd": "", "effectType": "bullet_god<PERSON>ace", "conditionType": "passive", "target": "me", "duration": "2", "mul": "", "range": "600", "iconUrl": ""}, {"name": "heroSprint", "cnName": "鬼步", "father": "unionSkill", "index": "", "description": "人物在空中，双击前进或者后退按钮，可以向指定方向冲刺一段距离。", "cd": "", "effectType": "heroSprint", "conditionType": "passive", "target": "me", "duration": "0.3", "mul": "0", "range": "", "iconUrl": ""}, {"name": "godEyesDefence", "cnName": "上帝之眼-防御力降低", "father": "unionSkill", "index": "0", "description": "击中目标后削弱其[mul-1]的防御力。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "target", "duration": "20", "mul": "1.1", "range": "", "iconUrl": ""}, {"name": "godMaceDefence", "cnName": "上帝之杖-防御力降低", "father": "unionSkill", "index": "0", "description": "击中目标后削弱其[mul-1]的防御力。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "target", "duration": "20", "mul": "1.2", "range": "", "iconUrl": ""}, {"name": "petHurtHole", "cnName": "暴力尸宠", "father": "unionSkill", "index": "", "description": "提升我方尸宠[mul-1]的伤害。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "me,range,we,pet", "duration": "2", "mul": "1.15", "range": "99999", "iconUrl": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cnName": "虚幻猎手", "father": "unionSkill", "index": "", "description": "对虚幻塔的敌人造成额外[mul-1]的伤害。", "cd": "", "effectType": "changeHurtNoCondition", "conditionType": "passive", "target": "me", "duration": "", "mul": "1.2", "range": "", "iconUrl": ""}], "276loveSkillXiaoMeiClass": [{"name": "cdMulMei", "cnName": "技能回复速度提升20%", "father": "loveSkill", "index": "", "description": "技能回复速度提升[mul-1]。", "cd": "", "effectType": "cdSpeed", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "1.2", "range": "", "iconUrl": ""}, {"name": "cdHoleMei", "cnName": "技能回复光环", "father": "loveSkill", "index": "", "description": "提升周围[range]码内队友[mul-1]的技能回复速度。", "cd": "", "effectType": "cdSpeed", "conditionType": "passive", "target": "me,range,we", "duration": "0.2", "mul": "1.3", "range": "200", "iconUrl": ""}, {"name": "angerHoleMei", "cnName": "怒气回复光环", "father": "loveSkill", "index": "", "description": "提升周围[range]码内队友[mul-1]的怒气回复速度。", "cd": "", "effectType": "angerAddMul", "conditionType": "passive", "target": "me,range,we", "duration": "0.2", "mul": "1.4", "range": "130", "iconUrl": ""}, {"name": "dieOffMei", "cnName": "封锁", "father": "loveSkill", "index": "", "description": "死后将封锁凶手[duration]秒。", "cd": "", "effectType": "noAllSkill", "conditionType": "passive", "target": "target", "duration": "10", "mul": "", "range": "", "iconUrl": ""}, {"name": "dieNiuBiMei", "cnName": "无敌", "father": "loveSkill", "index": "", "description": "死后所有队友将无敌[duration]秒。", "cd": "", "effectType": "invincible", "conditionType": "passive", "target": "me,range,we", "duration": "5", "mul": "", "range": "99999", "iconUrl": ""}, {"name": "degradationMei", "cnName": "退化光环", "father": "loveSkill", "index": "", "description": "降低所有敌人的[mul-1]的防御力。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "me,range,enemy", "duration": "1", "mul": "1.13", "range": "999999", "iconUrl": ""}], "283loveSkillZangShiClass": [{"name": "dodgeProZang", "cnName": "闪避提升20%", "father": "loveSkill", "index": "", "description": "战斗中提升20%的闪避。", "cd": "", "effectType": "dodge<PERSON>ro", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "", "range": "", "iconUrl": ""}, {"name": "defenceZang", "cnName": "防御力提升30%", "father": "loveSkill", "index": "", "description": "战斗中提升[1-mul]的防御力。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "0.7", "range": "", "iconUrl": ""}, {"name": "hurtStrikerZang", "cnName": "P1增伤-藏师", "father": "loveSkill", "index": "", "description": "战斗中提升P1角色[mul-1]的攻击力。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "1.1", "range": "", "iconUrl": ""}, {"name": "noHurtZang", "cnName": "抵挡伤害", "father": "loveSkill", "index": "", "description": "有[effectProArr.0]的几率抵挡超过自身生命值30%的伤害。", "cd": "", "effectType": "noHurt", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "sameArmsHurtAddZang", "cnName": "相同武器伤害叠加", "father": "loveSkill", "index": "", "description": "队友手中持有和藏师相同枪支时，攻击力提升[mul]。", "cd": "", "effectType": "sameArmsHurtAdd", "conditionType": "passive", "target": "me,range,we", "duration": "0.5", "mul": "0.15", "range": "9999999", "iconUrl": ""}, {"name": "hurtZang", "cnName": "攻击力提升30%", "father": "loveSkill", "index": "", "description": "战斗中的攻击力提升[mul-1]。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "1.3", "range": "", "iconUrl": ""}, {"name": "hurtHoleZang", "cnName": "伤害光环150", "father": "loveSkill", "index": "", "description": "提升周围[range]码内队友[mul-1]的攻击力。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "me,range,we", "duration": "0.2", "mul": "1.4", "range": "150", "iconUrl": ""}], "294equipSkillClass": [{"name": "godHand_equip", "cnName": "上帝的护佑", "father": "headSkill", "index": "0", "description": "在受到必亡的伤害时，你将接受上帝的护佑，进入隐身无敌模式，持续[duration]秒，同时剩余1点生命值。技能触发间隔不小于50秒。", "cd": "", "effectType": "godHand", "conditionType": "passive", "target": "me", "duration": "3", "mul": "", "range": "", "iconUrl": ""}, {"name": "immune_equip", "cnName": "免疫", "father": "headSkill", "index": "0", "description": "当敌人对你释放技能时，有[effectProArr.0]几率使该技能对你无效。", "cd": "", "effectType": "immune", "conditionType": "passive", "target": "me", "duration": "", "mul": "1", "range": "", "iconUrl": ""}, {"name": "magneticField_equip", "cnName": "磁力场", "father": "headSkill", "index": "0", "description": "每过[intervalT]秒向外释放磁力场，使700码的范围内的所有敌方子弹偏离轨道，持续[duration]秒。", "cd": "", "effectType": "magneticB", "conditionType": "passive", "target": "me", "duration": "7", "mul": "0.2", "range": "200", "iconUrl": ""}, {"name": "strongHalo_equip", "cnName": "顽强光环", "father": "headSkill", "index": "0", "description": "为周围[range]码以内的我方单位增加[1-mul]的防御力。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "me,range,we", "duration": "2", "mul": "0.85", "range": "800", "iconUrl": ""}, {"name": "murderous_equip", "cnName": "嗜爪之怒", "father": "headSkill", "index": "0", "description": "每次受到攻击都有[effectProArr.0]的几率进入嗜爪状态，增加[mul-1]的攻击力，持续[duration]秒。技能触发间隔不小于[minTriggerT]秒。", "cd": "", "effectType": "murderous_add<PERSON><PERSON><PERSON><PERSON>", "conditionType": "passive", "target": "me", "duration": "6", "mul": "2.5", "range": "", "iconUrl": ""}, {"name": "poisonRange_equip", "cnName": "瘴气", "father": "headSkill", "index": "0", "description": "每次释放任何主动技能，都会使周围[range]码内的敌人中毒，每秒受到伤害，伤害值为施法者当前武器实际战斗力的[mul]，持续[duration]秒。", "cd": "", "effectType": "poison", "conditionType": "passive", "target": "me,range,enemy", "duration": "5", "mul": "0.5", "range": "450", "iconUrl": ""}, {"name": "attackSpeedHalo_equip", "cnName": "耐久光环", "father": "headSkill", "index": "0", "description": "为周围[range]码以内的我方射击单位增加[mul-1]的攻击速度。", "cd": "", "effectType": "attackGapMul", "conditionType": "passive", "target": "me,range,we", "duration": "2", "mul": "1.13", "range": "700", "iconUrl": ""}, {"name": "sacrifice_equip", "cnName": "牺牲", "father": "coatSkill", "index": "0", "description": "每次受到伤害都能增加[range]范围内我方单位的生命值，增加值为伤害值的[mul]。", "cd": "", "effectType": "sacrifice", "conditionType": "passive", "target": "me,range,we", "duration": "", "mul": "0.15", "range": "450", "iconUrl": ""}, {"name": "backStrong_equip", "cnName": "钢背", "father": "coatSkill", "index": "0", "description": "减少来自背面[1-mul]的伤害。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.65", "range": "", "iconUrl": ""}, {"name": "anionSkin_equip", "cnName": "负离子外壳", "father": "coatSkill", "index": "0", "description": "自身被攻击时，攻击者有[effectProArr.0]的几率被麻痹，无法移动，攻击速度降低[1-mul]，持续[duration]秒。", "cd": "", "effectType": "anionSkin", "conditionType": "passive", "target": "target", "duration": "3", "mul": "0.5", "range": "", "iconUrl": ""}, {"name": "treater_equip", "cnName": "净化器", "father": "coatSkill", "index": "0", "description": "每隔[intervalT]秒清除1次自身负面状态。", "cd": "", "effectType": "clearEnemyState", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "backWeak_equip", "cnName": "芒刺", "father": "coatSkill", "index": "0", "description": "攻击敌人背部或者后脑勺都会造成额外[mul-1]的伤害。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.35", "range": "", "iconUrl": ""}, {"name": "thornSkin_equip", "cnName": "荆棘外表", "father": "coatSkill", "index": "0", "description": "自身被攻击时，攻击者有[effectProArr.0]的几率进入眩晕状态，持续[duration]秒。", "cd": "", "effectType": "dizziness", "conditionType": "passive", "target": "target", "duration": "2", "mul": "", "range": "", "iconUrl": ""}, {"name": "refraction_equip", "cnName": "折射", "father": "coatSkill", "index": "0", "description": "自身被攻击时，有[effectProArr.0]的几率不受伤害，同时使周围[range]内的敌人受到同等伤害。", "cd": "", "effectType": "refraction", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "350", "iconUrl": ""}, {"name": "zoomOut", "cnName": "装甲压制", "father": "fashionSkill", "index": "", "description": "在骑乘赤焰、幽鬼等街跑载具时，每隔[intervalT]秒，向敌方丢出3辆变大的玩具坦克，产生3*武器面板伤害*射速的爆炸伤害。", "cd": "", "effectType": "bullet_zoomOut", "conditionType": "passive", "target": "me", "duration": "", "mul": "3", "range": "99999", "iconUrl": ""}, {"name": "refraction_equip_link", "cnName": "折射-不受伤害", "father": "equipSkill_link", "index": "0", "description": "", "cd": "", "effectType": "noUnderHurtB", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}], "295wenJieSkillClass": [{"name": "underMurderous_jie", "cnName": "反制", "father": "heroSkill", "index": "", "description": "受到攻击时单位增加[mul-1]的伤害，防御力增加[secMul-1]，持续[duration]秒。{font color='#00FF00'}14级无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "underMurderous_jie", "conditionType": "passive", "target": "me", "duration": "2", "mul": "1.2", "range": "", "iconUrl": "SkillIcon/sacrifice"}, {"name": "vacuum<PERSON>ie", "cnName": "真空", "father": "heroSkill", "index": "", "description": "使500码范围内的我方单位无敌1.5秒（虚天塔中只会提高防御力），并将全图敌人吸附到自己身边（争霸中无效），上限40个。同时降低这些敌人[1-mul]的防御力，持续[duration]秒；自身载具也可使用该技能。", "cd": "5", "effectType": "vacuum<PERSON>ie", "conditionType": "active", "target": "me,range,enemy", "duration": "2", "mul": "0.50", "range": "99999", "iconUrl": "SkillIcon/vacuumJie"}, {"name": "armsSpeed_jie", "cnName": "加速扳机", "father": "heroSkill", "index": "", "description": "增加所有武器[mul-1]的射速（赤鼬除外）。对看门狗、斩之使者额外造成[secMul-1]的伤害。{font color='#00FF00'}14级无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "armsSpeed_jie", "conditionType": "passive", "target": "me", "duration": "0.3", "mul": "1.1", "range": "", "iconUrl": "SkillIcon/armsSpeed_jie"}, {"name": "合金护甲", "cnName": "合金护甲", "father": "heroSkill", "index": "", "description": "减少[1-mul]受到的百分比伤害。{font color='#00FF00'}14级无需装备技能也会有技能效果。{/font}", "cd": "", "effectType": "changeHurtMul", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.91", "range": "", "iconUrl": "SkillIcon/alloyShell"}, {"name": "真空-无敌", "cnName": "真空-无敌", "father": "heroSkillLink", "index": "0", "description": "", "cd": "", "effectType": "vacuumJie_link", "conditionType": "active", "target": "me,range,we", "duration": "1.5", "mul": "", "range": "500", "iconUrl": ""}], "29loveSkillClass": [{"name": "敏捷馈赠", "cnName": "敏捷馈赠", "father": "loveSkill", "index": "", "description": "P1角色在战斗中获得相当于闪避概率[mul]的攻击力。", "cd": "", "effectType": "hurtAddByLove", "conditionType": "passive", "target": "target", "duration": "", "mul": "0.4", "range": "", "iconUrl": ""}, {"name": "防御力提升", "cnName": "防御力提升", "father": "loveSkill", "index": "", "description": "战斗中永久提升[1-mul]的防御力。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "0.5", "range": "", "iconUrl": ""}, {"name": "攻击力提升", "cnName": "攻击力提升", "father": "loveSkill", "index": "", "description": "战斗中永久提升小樱[mul-1]的攻击力。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "1.3", "range": "", "iconUrl": ""}, {"name": "真情之力", "cnName": "真情之力", "father": "loveSkill", "index": "", "description": "战斗中永久提升P1角色[mul-1]的攻击力和防御力。", "cd": "", "effectType": "hurtDefence", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "1.3", "range": "", "iconUrl": ""}, {"name": "rebirth_loveSkill", "cnName": "重生", "father": "loveSkill", "index": "", "description": "单位倒下重生，回复[mul]的生命值，一次战斗只能重生1次。", "cd": "", "effectType": "rebirthAndHiding", "conditionType": "passive", "target": "me", "duration": "5", "mul": "0.9", "range": "", "iconUrl": ""}, {"name": "回复", "cnName": "回复", "father": "loveSkill", "index": "", "description": "每秒回复最大生命值[mul]的生命值。", "cd": "", "effectType": "life", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.03", "range": "", "iconUrl": ""}, {"name": "真情治愈", "cnName": "真情治愈", "father": "loveSkill", "index": "", "description": "战斗中每隔[intervalT]秒，小樱产出1瓶血瓶，拾取可以回复30%生命值，只有P1角色可以拾取。", "cd": "", "effectType": "lifeBottle_loveSkill", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "element_loveSkill", "cnName": "分子虹吸", "father": "loveSkill", "index": "", "description": "使[range]范围内的敌人每秒损失0.5%的血量（血量低于70%后无效），当范围内有敌方单位存在，自身每秒回复2%的生命。修罗模式下效果降低。", "cd": "", "effectType": "element_loveSkill", "conditionType": "passive", "target": "me,range,enemy", "duration": "", "mul": "0.003", "range": "800", "iconUrl": ""}, {"name": "elementLifeBack_loveSkill", "cnName": "分子虹吸-自身回血", "father": "loveSkill", "index": "", "description": "", "cd": "", "effectType": "elementLifeBack_loveSkill", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.02", "range": "800", "iconUrl": ""}], "300xinLingSkillClass": [{"name": "嘲讽", "cnName": "嘲讽", "father": "heroSkill", "index": "", "description": "自身无敌[duration]秒，期间逼迫900码内的敌人攻击自己。更高等级技能将会反弹伤害给敌人。", "cd": "70", "effectType": "invincible", "conditionType": "active", "target": "me", "duration": "6", "mul": "", "range": "", "iconUrl": ""}, {"name": "遇强则刚", "cnName": "遇强则刚", "father": "heroSkill", "index": "", "description": "当目标敌人处于无敌状态时，自己和主角受到伤害降低[1-mul]、百分比伤害降低[1-secMul]。", "cd": "", "effectType": "strongLing", "conditionType": "passive", "target": "me,range,we", "duration": "1", "mul": "0.9", "range": "99999", "iconUrl": ""}, {"name": "复仇", "cnName": "复仇", "father": "heroSkill", "index": "", "description": "我方单位殒命时，对生命值高于20%的凶手造成百分比伤害（角色殒命造成[mul]伤害，其他殒命造成3%伤害），对同个凶手最多造成[secMul]的伤害。99级主线任务中该技能无效。", "cd": "", "effectType": "revengeLingAddSkill", "conditionType": "passive", "target": "me,range,we", "duration": "99999", "mul": "0.04", "range": "99999", "iconUrl": ""}, {"name": "共鸣", "cnName": "共鸣", "father": "heroSkill", "index": "", "description": "当自己和队友持有同一类型枪支时，双方伤害提升[mul]。", "cd": "", "effectType": "resonanceLing", "conditionType": "passive", "target": "me,range,we", "duration": "0.5", "mul": "0.03", "range": "9999999", "iconUrl": ""}, {"name": "暗夜信徒", "cnName": "暗夜信徒", "father": "heroSkill", "index": "", "description": "在星空或月亮的场景下，继承当前主角[mul]的战斗力，继承战斗力不超过自身战斗力的[secMul]。", "cd": "", "effectType": "nightLing", "conditionType": "passive", "target": "me", "duration": "999999", "mul": "0.05", "range": "", "iconUrl": ""}, {"name": "恶爪", "cnName": "恶爪", "father": "heroSkill", "index": "", "description": "让所有敌人身上的负面状态时间延长[mul-1]。", "cd": "", "effectType": "setEnemyStateTMul", "conditionType": "passive", "target": "me,range,enemy", "duration": "", "mul": "1.03", "range": "99999", "iconUrl": ""}, {"name": "tauntLingBack_link", "cnName": "反弹伤害", "father": "heroSkillLink", "index": "", "description": "", "cd": "", "effectType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "conditionType": "passive", "target": "target", "duration": "", "mul": "0", "range": "", "iconUrl": ""}, {"name": "revengeLing_link", "cnName": "复仇伤害", "father": "heroSkillLink", "index": "", "description": "", "cd": "", "effectType": "revengeLingHurt", "conditionType": "passive", "target": "target", "duration": "", "mul": "0.04", "range": "", "iconUrl": ""}], "324armsSkillClass": [{"name": "击中减速", "cnName": "击中减速", "father": "armsSkill", "index": "", "description": "击中目标后减少其[1-mul]的移动速度，持续2秒。", "cd": "", "effectType": "moveSpeed", "conditionType": "passive", "target": "target", "duration": "2", "mul": "0.6", "range": "0", "iconUrl": ""}, {"name": "击毙补充弹药", "cnName": "击毙补充弹药", "father": "armsSkill", "index": "", "description": "击毙单位后，补充当前武器5%的携弹量。", "cd": "", "effectType": "charger", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.05", "range": "", "iconUrl": ""}, {"name": "击毙回复", "cnName": "击毙回复", "father": "armsSkill", "index": "", "description": "击毙单位后，补充自身4%的生命值。", "cd": "", "effectType": "life", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.04", "range": "", "iconUrl": ""}, {"name": "剧毒", "cnName": "剧毒", "father": "armsSkill", "index": "", "description": "击中目标后，使其每秒减少一定的生命值，减少量为武器当前造成伤害的20%，持续5秒。", "cd": "", "effectType": "poison", "conditionType": "passive", "target": "target", "duration": "5", "mul": "0.2", "range": "", "iconUrl": ""}, {"name": "击毙溅射", "cnName": "击毙溅射", "father": "armsSkill", "index": "", "description": "击毙目标后，对其200范围内的敌方单位造成伤害，伤害值为武器实际战斗力的30%。", "cd": "", "effectType": "spurting", "conditionType": "passive", "target": "target,range,enemy", "duration": "", "mul": "0.3", "range": "200", "iconUrl": ""}, {"name": "振奋", "cnName": "振奋", "father": "armsSkill", "index": "", "description": "击毙敌人后，自身攻击力提升20%，持续2秒。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "me", "duration": "2", "mul": "1.2", "range": "", "iconUrl": ""}, {"name": "击中溅射", "cnName": "击中溅射", "father": "armsSkill", "index": "", "description": "击中目标后，对其200范围内的敌方单位造成伤害，伤害值为武器所造成伤害的10%。", "cd": "", "effectType": "spurting", "conditionType": "passive", "target": "target,range,enemy,,,6", "duration": "", "mul": "0.1", "range": "200", "iconUrl": ""}, {"name": "致残", "cnName": "致残", "father": "armsSkill", "index": "", "description": "击中目标后减少其25%的攻击力，持续2秒。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "target", "duration": "2", "mul": "0.75", "range": "", "iconUrl": ""}, {"name": "击中麻痹", "cnName": "击中麻痹", "father": "armsSkill", "index": "", "description": "击中目标后，有[effectProArr.0]的几率使其麻痹，持续[duration]秒。", "cd": "", "effectType": "moveSpeed", "conditionType": "passive", "target": "target", "duration": "2", "mul": "0", "range": "", "iconUrl": ""}, {"name": "致盲", "cnName": "致盲", "father": "armsSkill", "index": "", "description": "击中目标后使其失去准心，使其攻击成功率降低[mul]，持续3秒。", "cd": "", "effectType": "lost<PERSON><PERSON>", "conditionType": "passive", "target": "target", "duration": "3", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "击中回复", "cnName": "击中回复", "father": "armsSkill", "index": "", "description": "击中目标后，补充自身生命值，补充值为武器所造成伤害的1%。", "cd": "", "effectType": "Hit_AddLifeMul", "conditionType": "passive", "target": "me", "duration": "", "mul": "0", "range": "", "iconUrl": ""}, {"name": "击中沉默", "cnName": "击中沉默", "father": "armsSkill", "index": "", "description": "击中目标后，有[effectProArr.0]的几率使其无法释放技能，持续[duration]秒，同时清除目标身上一些状态。", "cd": "", "effectType": "silenceBAndClearState", "conditionType": "passive", "target": "target", "duration": "2", "mul": "", "range": "", "iconUrl": ""}, {"name": "击中派生", "cnName": "击中派生", "father": "armsSkill", "index": "", "description": "每次射击都有[effectProArr.0]的概率派生跟踪导弹。导弹伤害为当前武器实际战斗力值。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "震动炮", "cnName": "震动炮", "father": "godArmsSkill", "index": "", "description": "敌人移动时将会引爆你子弹内的“震动炮”，对敌人额外造成[mul-1]的伤害。无视技能免疫。", "cd": "", "effectType": "throwExplodeGod", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.4", "range": "", "iconUrl": ""}, {"name": "背刺", "cnName": "背刺", "father": "godArmsSkill", "index": "", "description": "当你面朝着敌人背面时，对敌人可造成额外[mul-1]的伤害，如果敌人以你为攻击目标时，则该伤害则会提升至[secMul-1]。无视技能免疫。", "cd": "", "effectType": "backstabGod", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.6", "range": "", "iconUrl": ""}, {"name": "implodingGod", "cnName": "爆岩", "father": "godArmsSkill", "index": "", "description": "每次击中目标都有[effectProArr.0]的几率使自身爆发5颗石头，每颗石头伤害值为：[mul]*武器面板伤害*增伤Buff（排除武器技能、零件技能）。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "25", "range": "", "iconUrl": ""}, {"name": "裂空波", "cnName": "裂空波", "father": "godArmsSkill", "index": "", "description": "对离开地面的敌人施加“裂空波”，对其造成额外[mul-1]的伤害；如果敌人为空中单位，则只造成额外[secMul-1]的伤害。无视技能免疫。", "cd": "", "effectType": "airHurtGod", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.7", "range": "", "iconUrl": ""}, {"name": "穿刺", "cnName": "穿刺", "father": "godArmsSkill", "index": "", "description": "子弹穿人后不衰减，且每次穿人增伤[mul]，最大增伤[secMul]。无视技能免疫。", "cd": "", "effectType": "punctureGod", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.4", "range": "", "iconUrl": ""}, {"name": "弹力回收", "cnName": "弹力回收", "father": "godArmsSkill", "index": "", "description": "子弹每次反弹增伤[mul]，最大增伤[secMul]。无视“空虚”技能，无视技能免疫。", "cd": "", "effectType": "elasticRecoveryGod", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.5", "range": "", "iconUrl": ""}, {"name": "盛血绞杀", "cnName": "盛血绞杀", "father": "godArmsSkill", "index": "", "description": "对于生命值大于[mul]的敌人造成[secMul]伤害。无视免疫和技能免疫。", "cd": "", "effectType": "fullCrushGod", "conditionType": "passive", "target": "target", "duration": "", "mul": "0.95", "range": "", "iconUrl": ""}, {"name": "膝跳反应", "cnName": "膝跳反应", "father": "godArmsSkill", "index": "", "description": "每次射击都会让敌人不由自主的弹跳，并对敌人额外造成[mul-1]的伤害。无视免疫和技能免疫。", "cd": "", "effectType": "jumpAutoGod", "conditionType": "passive", "target": "target", "duration": "0.2", "mul": "1.15", "range": "", "iconUrl": ""}, {"name": "静电反应", "cnName": "静电反应", "father": "godArmsSkill", "index": "", "description": "子弹经过隐身敌人时产生“静电反应”，对其额外造成[mul-1]的伤害。无视技能免疫。", "cd": "", "effectType": "hide<PERSON>urt<PERSON>od", "conditionType": "passive", "target": "target", "duration": "", "mul": "3", "range": "", "iconUrl": ""}, {"name": "迷失电流", "cnName": "迷失电流", "father": "godArmsSkill", "index": "", "description": "给击中敌人下身加上一层“迷失电流”，使它无法瞬移，但会增加敌人[mul-1]的移动速度，持续[duration]秒。无视技能免疫。", "cd": "", "effectType": "positionErrorGod", "conditionType": "passive", "target": "target", "duration": "0.5", "mul": "1.3", "range": "", "iconUrl": ""}, {"name": "共振", "cnName": "共振", "father": "godArmsSkill", "index": "", "description": "随机选一把携带的同类武器，触发其神级技能（突袭、粘性、跳斩、子母弹、烟花、轨迹绘制、弹爆等武器技能无效），最多触发2个，且不会触发已有技能。装有改变品质成色的零件时该技能无效。", "cd": "", "effectType": "resonateGod", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "老练", "cnName": "老练", "father": "godArmsSkill", "index": "", "description": "从武器获得之日起，每过1天，武器在战斗中的伤害就增加[mul]，最高增加[secMul]（装有改变品质成色的零件时，最高增加30%）。", "cd": "", "effectType": "seasonedGod", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.05", "range": "", "iconUrl": ""}, {"name": "远射", "cnName": "远射", "father": "godArmsSkill", "index": "", "description": "击中敌人后给自己附上增伤状态，增加伤害=(武器射程-[value])*0.0001，最高增加[secMul]，持续[duration]秒。", "cd": "", "effectType": "longShootGod", "conditionType": "passive", "target": "me", "duration": "3", "mul": "0.0001", "range": "", "iconUrl": ""}, {"name": "超重", "cnName": "超重", "father": "godArmsSkill", "index": "", "description": "使敌人重力变大，持续时间是武器初始射击间隔的3倍。", "cd": "", "effectType": "overweightGod", "conditionType": "passive", "target": "target", "duration": "0.1", "mul": "", "range": "", "iconUrl": ""}, {"name": "瞬秒", "cnName": "瞬秒", "father": "godArmsSkill", "index": "", "description": "当敌人的血量低于60%同时血量少于这把武器实际战斗力的200倍时，击中它就有[effectProArr.0]的几率直接击毙它！", "cd": "", "effectType": "seckill", "conditionType": "passive", "target": "target", "duration": "", "mul": "50", "range": "", "iconUrl": ""}, {"name": "绝灭", "cnName": "绝灭", "father": "godArmsSkill", "index": "", "description": "当敌人的血量低于60%同时血量少于这把武器实际战斗力的80倍时，击中它就有[effectProArr.0]的几率直接击毙它！", "cd": "", "effectType": "seckill", "conditionType": "passive", "target": "target", "duration": "", "mul": "20", "range": "", "iconUrl": ""}, {"name": "快感", "cnName": "快感", "father": "godArmsSkill", "index": "", "description": "击中目标有[effectProArr.0]的几率使自己进入狂暴状态，攻击速度加倍，不消耗子弹（非修罗模式），持续4秒。", "cd": "", "effectType": "<PERSON><PERSON><PERSON>", "conditionType": "passive", "target": "me", "duration": "3", "mul": "1.5", "range": "", "iconUrl": ""}, {"name": "搏命", "cnName": "搏命", "father": "godArmsSkill", "index": "", "description": "自身生命值低于[obj.per]的时候，生命值越低攻击力越高，最高加成150%的攻击力。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "target", "duration": "", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "七步毒", "cnName": "七步毒", "father": "godArmsSkill", "index": "", "description": "击中目标后，目标移动身体则每0.2秒都将受到伤害，伤害值为武器所造成伤害的[mul]。状态持续[duration]秒。", "cd": "", "effectType": "poison7", "conditionType": "passive", "target": "target", "duration": "3", "mul": "0.2", "range": "", "iconUrl": ""}, {"name": "Hit_pointBoom_godArmsSkill", "cnName": "引爆", "father": "godArmsSkill", "index": "", "description": "每次击中目标都有[effectProArr.0]的几率在目标处引爆炸弹，对周围100码内的敌人造成伤害，伤害值为武器实际战斗力的2倍。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "Hit_fleshSkill_godArmsSkill", "cnName": "刷新", "father": "godArmsSkill", "index": "", "description": "每次击中目标都有[effectProArr.0]的几率使最近使用的那一个主动技能的冷却时间回复20%。", "cd": "", "effectType": "fleshLastSkill", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.15", "range": "", "iconUrl": ""}, {"name": "Hit_imploding_godArmsSkill", "cnName": "爆石", "father": "godArmsSkill", "index": "", "description": "每次击中目标都有[effectProArr.0]的几率使自身爆发5颗石头，每颗石头伤害值为：[mul]*武器面板伤害*增伤Buff（排除武器技能、零件技能）。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "40", "range": "", "iconUrl": ""}, {"name": "击中闪电", "cnName": "击中闪电", "father": "godArmsSkill", "index": "", "description": "击中敌人后有[effectProArr.0]的几率产生高压电流，对[range]码内最近的敌人造成1倍于武器实际战斗力的伤害。", "cd": "", "effectType": "lightning_godArmsSkill", "conditionType": "passive", "target": "me,near,enemy", "duration": "", "mul": "1", "range": "300", "iconUrl": ""}, {"name": "击中眩晕", "cnName": "击中眩晕", "father": "godArmsSkill", "index": "2", "description": "击中目标有[effectProArr.0]的几率使目标陷入眩晕状态，持续[duration]秒。", "cd": "", "effectType": "dizziness", "conditionType": "passive", "target": "target", "duration": "2", "mul": "", "range": "", "iconUrl": ""}, {"name": "超级派生", "cnName": "超级派生", "father": "godArmsSkill", "index": "3", "description": "每次射击都有9%的概率派生跟踪导弹。导弹伤害为当前武器实际战斗力值。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "1", "range": "", "iconUrl": ""}, {"name": "超级减速", "cnName": "超级减速", "father": "godArmsSkill", "index": "", "description": "击中目标后减少其[1-mul]的移动速度，持续2秒。无视技能免疫。", "cd": "", "effectType": "moveSpeed", "conditionType": "passive", "target": "target", "duration": "2", "mul": "0.5", "range": "0", "iconUrl": ""}, {"name": "超级麻痹", "cnName": "超级麻痹", "father": "godArmsSkill", "index": "", "description": "击中目标后，有[effectProArr.0]的几率使其麻痹，持续[duration]秒。", "cd": "", "effectType": "moveSpeed", "conditionType": "passive", "target": "target", "duration": "4", "mul": "0", "range": "", "iconUrl": ""}, {"name": "炎爆", "cnName": "炎爆", "father": "godArmsSkill", "index": "", "description": "每次伤害都会给目标叠加1层持续[duration]秒的“火焰”，当叠加到[value]层时对目标造成巨大的爆炸伤害。", "cd": "", "effectType": "burn_arms", "conditionType": "passive", "target": "target", "duration": "2", "mul": "", "range": "", "iconUrl": ""}, {"name": "冷凝", "cnName": "冷凝", "father": "godArmsSkill", "index": "", "description": "每次伤害都会给目标叠加1层持续[duration]秒的“霜冻”，每层降低目标[mul]的防御力，最高叠加[value]层。", "cd": "", "effectType": "cold_arms", "conditionType": "passive", "target": "target", "duration": "2", "mul": "0.005", "range": "", "iconUrl": ""}, {"name": "蚀骨", "cnName": "蚀骨", "father": "godArmsSkill", "index": "", "description": "每次伤害都会给目标叠加1层持续[duration]秒的“腐蚀”，每层降低目标[mul]的防御力，最高叠加[value]层。", "cd": "", "effectType": "cold_arms", "conditionType": "passive", "target": "target", "duration": "2", "mul": "0.01", "range": "", "iconUrl": ""}, {"name": "痛击", "cnName": "痛击", "father": "godArmsSkill", "index": "", "description": "击中同时推动目标一段距离，蓄力越久推动距离越远，无视技能免疫。", "cd": "", "effectType": "beatBack_arms", "conditionType": "passive", "target": "target", "duration": "2", "mul": "", "range": "", "iconUrl": ""}, {"name": "连弩", "cnName": "连弩", "father": "godArmsSkill", "index": "", "description": "每次射击都将发射连弩，蓄力越久连弩数量越多，单发连弩伤害为武器面板单伤*射速*[mul]。", "cd": "", "effectType": "bullet_combo", "conditionType": "passive", "target": "me", "duration": "2", "mul": "0.04", "range": "", "iconUrl": ""}, {"name": "viscous_ArmsSkill", "cnName": "粘性", "father": "godArmsSkill", "index": "", "description": "礼炮发射出的糖果会粘在地面或者墙壁上，持续30秒，怪物经过时会引爆糖果。", "cd": "", "effectType": "no", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "超级粘性", "cnName": "超级粘性", "father": "godArmsSkill", "index": "", "description": "发射的糖果会粘在地面或墙上，敌人经过时会引爆糖果，同时降低敌人攻击成功率。", "cd": "", "effectType": "lost<PERSON><PERSON>", "conditionType": "passive", "target": "target", "duration": "3", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "火焰始祖", "cnName": "火焰始祖", "father": "godArmsSkill", "index": "", "description": "连续射击时候，每隔一段时间喷射出异祖龙的火焰，追击敌方单位，无子弹时候也能触发。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "0.4", "mul": "", "range": "", "iconUrl": ""}, {"name": "贯穿波", "cnName": "贯穿波", "father": "godArmsSkill", "index": "", "description": "连续射击时，每隔一段时间产生一个缓慢移动的光波，贯穿所有敌方单位。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "1", "mul": "", "range": "", "iconUrl": ""}, {"name": "无限火力", "cnName": "无限火力", "father": "godArmsSkill", "index": "", "description": "连续射击超过1秒，武器将不消耗子弹。", "cd": "", "effectType": "noReduceCapacityB", "conditionType": "passive", "target": "me", "duration": "0.5", "mul": "", "range": "", "iconUrl": ""}, {"name": "跳斩", "cnName": "跳斩", "father": "godArmsSkill", "index": "", "description": "使用副手（不包括死神副手）时，会瞬移到鼠标位置，发起跳斩攻击。", "cd": "", "effectType": "no", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "上帝之杖", "cnName": "上帝之杖", "father": "godArmsSkill", "index": "", "description": "击中目标后有[effectProArr.0]概率对目标造成范围伤害，敌人越多伤害越高。", "cd": "", "effectType": "godMace_ArmsSkill", "conditionType": "passive", "target": "target", "duration": "0.2", "mul": "", "range": "250", "iconUrl": ""}, {"name": "风雷", "cnName": "风雷", "father": "godArmsSkill", "index": "", "description": "移动能够积蓄电能，击中目标后消耗电能，同时产生6条闪电随机轰击附近的敌方单位。", "cd": "", "effectType": "windThunder", "conditionType": "passive", "target": "target", "duration": "0.2", "mul": "0.12", "range": "200", "iconUrl": ""}, {"name": "影灭", "cnName": "影灭", "father": "godArmsSkill", "index": "", "description": "下蹲状态下，每次射击都会额外向目标发射激光炮，激光伤害为当前武器面板单伤的20%。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "imploding_blackArmsSkill", "cnName": "爆沙", "father": "godArmsSkill", "index": "", "description": "击中目标后有[effectProArr.0]概率向前方爆裂出4条火花，轰炸前方所有目标，每条火花伤害为：20*武器面板伤害*增伤Buff（排除武器技能、零件技能）。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "20", "range": "", "iconUrl": ""}, {"name": "爆胆", "cnName": "爆胆", "father": "godArmsSkill", "index": "", "description": "击中后有[effectProArr.0]概率恐惧目标并造成额外伤害(武器面板单伤x[mul])。", "cd": "", "effectType": "armsFear", "conditionType": "passive", "target": "target", "duration": "2", "mul": "0.6", "range": "", "iconUrl": ""}, {"name": "爆震", "cnName": "爆震", "father": "godArmsSkill", "index": "", "description": "击中后有[effectProArr.0]概率恐惧目标并造成额外伤害(武器面板单伤x[mul])，并减速目标。", "cd": "", "effectType": "armsFear", "conditionType": "passive", "target": "target", "duration": "2", "mul": "1", "range": "", "iconUrl": ""}, {"name": "飞镰", "cnName": "飞镰", "father": "godArmsSkill", "index": "", "description": "击中目标后有[effectProArr.0]概率产生连续弹射的飞镰，飞镰最多弹射5次。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "4", "range": "", "iconUrl": ""}, {"name": "跟踪飞镰", "cnName": "跟踪飞镰", "father": "godArmsSkill", "index": "", "description": "击中目标后有[effectProArr.0]概率产生连续弹射的跟踪飞镰，无视防弹外壳、防弹钢甲，并对此类敌人额外造成40%的伤害。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "5", "range": "", "iconUrl": ""}, {"name": "战修罗", "cnName": "战修罗", "father": "godArmsSkill", "index": "", "description": "修罗模式下伤害增加[mul-1]。", "cd": "", "effectType": "changeHurtNoCondition", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.20", "range": "", "iconUrl": ""}, {"name": "突袭", "cnName": "突袭", "father": "godArmsSkill", "index": "", "description": "持续射击会封锁在场所有敌人的“抵御”技能。", "cd": "", "effectType": "noFoggyDefB", "conditionType": "passive", "target": "me,range,enemy", "duration": "0.2", "mul": "", "range": "999999", "iconUrl": ""}, {"name": "惩罚", "cnName": "惩罚", "father": "godArmsSkill", "index": "", "description": "对拥有技能净化器、免疫、技能免疫、抵御的敌人造成额外[mul-1]的伤害。", "cd": "", "effectType": "immuneNemesis", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.6", "range": "", "iconUrl": ""}, {"name": "打滑", "cnName": "打滑", "father": "godArmsSkill", "index": "", "description": "使敌人走路很滑，持续时间是武器初始射击间隔的2倍。", "cd": "", "effectType": "trackslipGod", "conditionType": "passive", "target": "target", "duration": "0.1", "mul": "", "range": "", "iconUrl": ""}, {"name": "超级共振", "cnName": "超级共振", "father": "godArmsSkill", "index": "", "description": "随机选一把携带武器（步枪、手枪、喷火器、能量枪必须为同类武器），触发其神级技能（突袭、粘性、跳斩、子母弹、烟花、轨迹绘制等武器技能无效）。装有改变品质成色的零件该技能无效。", "cd": "", "effectType": "resonateGod", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "lash_ArmsSkill", "cnName": "子母弹", "father": "godArmsSkill", "index": "", "description": "母弹自爆后内部弹射出7颗更小的子弹，每颗伤害为母弹的90%。", "cd": "", "effectType": "no", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "beadCrossbow_ArmsSkill", "cnName": "烟花", "father": "godArmsSkill", "index": "", "description": "这就是一把烟花炮！释放烟花后，坠落的烟花对敌人造成伤害。", "cd": "", "effectType": "no", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "陈年老糖", "cnName": "陈年老糖", "father": "godArmsSkill", "index": "", "description": "子弹每存在1秒，就增加[mul]的伤害，最高增加[secMul]的伤害。", "cd": "", "effectType": "oldSugar", "conditionType": "passive", "target": "target", "duration": "", "mul": "0.7", "range": "", "iconUrl": ""}, {"name": "editB<PERSON>etPath", "cnName": "轨迹绘制", "father": "godArmsSkill", "index": "", "description": "玩家可绘制子弹运动轨迹。", "cd": "", "effectType": "no", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "炎爆-爆", "cnName": "炎爆-爆", "father": "godArmsSkill_link", "index": "", "description": "火焰爆炸伤害。", "cd": "", "effectType": "crit", "conditionType": "passive", "target": "target", "duration": "", "mul": "7", "range": "", "iconUrl": ""}, {"name": "爆胆-无法叠加得buff", "cnName": "爆胆-无法叠加得buff", "father": "godArmsSkill_link", "index": "", "description": "", "cd": "", "effectType": "no", "conditionType": "passive", "target": "target", "duration": "3", "mul": "", "range": "", "iconUrl": ""}, {"name": "sickle_godArmsSkill2_link", "cnName": "超级飞镰-破甲", "father": "godArmsSkill_link", "index": "", "description": "对拥有技能防弹外壳、防弹钢甲的敌人造成额外[mul-1]的伤害。", "cd": "", "effectType": "overwhelmed", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.4", "range": "", "iconUrl": ""}], "334vehicleSkillClass": [{"name": "轰天聚合", "cnName": "轰天聚合", "father": "vehicleSkill", "index": "0", "description": "当在场的2个队友同时装备指定载具（必须强化至30级）时，他们和主角将合体成一个超强载具：轰天雷！伤害倍数：[mul]，持续时间[duration]秒。 指定载具包括黑暗泰坦、天目、判决者、腥红挖掘者、幽鬼、异祖龙、异齿虎、雷鸣以及它们的进化体。", "cd": "60", "effectType": "vehicleFit", "conditionType": "active", "target": "me", "duration": "20", "mul": "0.3", "range": "", "iconUrl": ""}, {"name": "镇山聚合", "cnName": "镇山聚合", "father": "vehicleSkill", "index": "0", "description": "在场的2个队友同时装备指定载具（必须强化至30级）时，他们和主角将合体成一个超强载具：镇山虎！伤害倍数：[mul]，持续时间[duration]秒。指定载具包括黑暗泰坦、血魂、天目、丛林狂袭者、月蚀、碾压者、赤焰、切割者。", "cd": "60", "effectType": "vehicleFit", "conditionType": "active", "target": "me", "duration": "20", "mul": "0.3", "range": "", "iconUrl": ""}, {"name": "霸空聚合", "cnName": "霸空聚合", "father": "vehicleSkill", "index": "0", "description": "在场的2个队友同时装备指定载具（必须强化至30级）时，他们和主角将合体成一个超强飞行器：霸空雕！伤害倍数：[mul]，持续时间[duration]秒。 指定载具包括丛林潜伏者、雄鹰、判决者、守望之翼、黑暗泰坦、血魂。", "cd": "60", "effectType": "vehicleFit", "conditionType": "active", "target": "me", "duration": "20", "mul": "0.3", "range": "", "iconUrl": ""}, {"name": "核弹头", "cnName": "核弹头", "father": "vehicleSkill", "index": "0", "description": "释放技能后，单位增加[mul-1]的射击速度，和一定的移动速度，持续[duration]秒。", "cd": "20", "effectType": "crazyVehicle", "conditionType": "active", "target": "me", "duration": "10", "mul": "1.21", "range": "", "iconUrl": ""}, {"name": "核动力", "cnName": "核动力", "father": "vehicleSkill", "index": "0", "description": "释放技能后，普通伤害、碾压伤害提升[mul-1]，持续[duration]秒。", "cd": "20", "effectType": "attackMul", "conditionType": "active", "target": "me", "duration": "10", "mul": "1.21", "range": "", "iconUrl": ""}, {"name": "守望之盾", "cnName": "守望之盾", "father": "vehicleSkill", "index": "0", "description": "在守望者冲刺时，减少[1-mul]受到的伤害。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "me", "duration": "0.6", "mul": "0.1", "range": "", "iconUrl": "SkillIcon/pioneer_hero"}, {"name": "合金外壳", "cnName": "合金外壳", "father": "vehicleSkill", "index": "", "description": "减少[1-mul]受到的百分比伤害。", "cd": "", "effectType": "changeHurtMul", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.91", "range": "", "iconUrl": "SkillIcon/alloyShell"}, {"name": "strongStrong", "cnName": "遇强则强", "father": "vehicleSkill", "index": "", "description": "攻击无敌怪物时，你的伤害会提高[mul-1]，持续[duration]秒。", "cd": "", "effectType": "otherHurtMul", "conditionType": "passive", "target": "me", "duration": "8", "mul": "1.08", "range": "", "iconUrl": "SkillIcon/strongStrong"}, {"name": "shockWave", "cnName": "冲击波", "father": "vehicleSkill", "index": "", "description": "自身爆炸后，震晕范围内[range]码的所有敌人[duration]秒。", "cd": "", "effectType": "dizziness", "conditionType": "passive", "target": "me,range,enemy", "duration": "0.4", "mul": "", "range": "600", "iconUrl": "SkillIcon/shockWave"}, {"name": "meetingGift", "cnName": "见面礼", "father": "vehicleSkill", "index": "", "description": "载具召唤出来后，会发射24颗导弹，对敌人进行轰炸，导弹伤害=角色战斗力x[mul]。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.008", "range": "300", "iconUrl": "SkillIcon/meetingGift"}, {"name": "狂战射手-击中麻痹", "cnName": "降低攻击力", "father": "vehicleSkillLink", "index": "0", "description": "击中目标后使其麻痹，持续2秒。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "target", "duration": "0.5", "mul": "0.6", "range": "", "iconUrl": ""}, {"name": "特效", "cnName": "特效", "father": "vehicleSkillLink", "index": "0", "description": "", "cd": "", "effectType": "no", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "", "range": "", "iconUrl": ""}, {"name": "特效", "cnName": "特效", "father": "vehicleSkillLink", "index": "0", "description": "", "cd": "", "effectType": "no", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "", "range": "", "iconUrl": ""}], "347loveSkillXinLingClass": [{"name": "防御力提升30%", "cnName": "防御力提升", "father": "loveSkill", "index": "", "description": "战斗中提升[1-mul]的防御力。", "cd": "", "effectType": "underHurtMul", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "0.6", "range": "", "iconUrl": ""}, {"name": "noHurt10Lings", "cnName": "抵挡低于自身生命值的伤害。", "father": "loveSkill", "index": "", "description": "抵挡低于自身生命值15%的伤害。", "cd": "", "effectType": "addLifeByHurt", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "resistPerLing4", "cnName": "抵挡4次百分比伤害", "father": "loveSkill", "index": "", "description": "关卡中抵挡4次百分比伤害。", "cd": "", "effectType": "noMulHurt", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "hurtStrikerLing", "cnName": "P1增伤-心零", "father": "loveSkill", "index": "", "description": "P1角色获得相当于好感度x0.00002的攻击力加成，最多加成40%。", "cd": "", "effectType": "hurtMulByLingLove", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "0.00002", "range": "", "iconUrl": ""}, {"name": "hurtLing40", "cnName": "攻击力提升40%", "father": "loveSkill", "index": "", "description": "战斗中的攻击力提升[mul-1]。", "cd": "", "effectType": "hurtMul", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "1.4", "range": "", "iconUrl": ""}, {"name": "vehicleLing40", "cnName": "载具防御力", "father": "loveSkill", "index": "", "description": "战斗中提升载具[mul-1]的防御力。", "cd": "", "effectType": "defenceAndAttack", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "1.5", "range": "", "iconUrl": ""}, {"name": "eleStrikerLing", "cnName": "指定元素加成", "father": "loveSkill", "index": "", "description": "战斗中，P1角色对指定元素敏感的敌人造成额外[mul-1]的伤害。周一指定生化敏感，周二、周五指定火焰敏感，周三、周六指定冷冻敏感，周四、周日指定电磁敏感。", "cd": "", "effectType": "hurtStrikerLing", "conditionType": "passive", "target": "target", "duration": "", "mul": "1.7", "range": "", "iconUrl": ""}, {"name": "dropStrikerLing", "cnName": "掉率提升", "father": "loveSkill", "index": "", "description": "P1角色消灭敌人后，武器装备碎片掉率、宝石掉率提升[mul]。", "cd": "", "effectType": "dropStrikerLing", "conditionType": "passive", "target": "me", "duration": "99999", "mul": "0.13", "range": "", "iconUrl": ""}], "62weaponSkillClass": [{"name": "weaponEmp", "cnName": "电离驱散", "father": "weaponSkill", "index": "", "description": "清除目标电离折射效果。", "cd": "", "effectType": "weaponEmp", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "weaponEmpAll", "cnName": "电离驱散-任何", "father": "weaponSkill", "index": "", "description": "清除目标电离折射效果。", "cd": "", "effectType": "clearStateByNameContainPointEffect", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "goldSpadeSkill", "cnName": "崩溃", "father": "weaponSkill", "index": "", "description": "用铁锹敲打首领（主线任务、虚天塔除外），使其防御力每秒下降[1-mul]，无视技能免疫，每天只能释放2次该技能。", "cd": "", "effectType": "goldSpadeSkill", "conditionType": "passive", "target": "target", "duration": "999999", "mul": "0.80", "range": "", "iconUrl": ""}, {"name": "凯撒特效附带", "cnName": "凯撒特效附带", "father": "weaponSkill", "index": "0", "description": "", "cd": "", "effectType": "sprintSword", "conditionType": "passive", "target": "target", "duration": "1", "mul": "", "range": "", "iconUrl": ""}, {"name": "凯撒特效附带", "cnName": "凯撒特效附带", "father": "weaponSkill", "index": "0", "description": "", "cd": "", "effectType": "no", "conditionType": "passive", "target": "", "duration": "", "mul": "", "range": "", "iconUrl": ""}], "75sceneSkillClass": [{"name": "stone<PERSON>eaBuff", "cnName": "石海buff", "father": "sceneSkill", "index": "", "description": "", "cd": "", "effectType": "stone<PERSON>eaBuff", "conditionType": "passive", "target": "me", "duration": "99999999", "mul": "0.03", "range": "", "iconUrl": ""}], "94demonSkillClass": [{"name": "shootSpeed2", "cnName": "高射速", "father": "demonSkill", "index": "", "description": "增加[mul-1]的射击速度，和[secMul-1]的换弹速度。", "cd": "", "effectType": "attackAndReloadSpeedMul", "conditionType": "passive", "target": "me", "duration": "999999", "mul": "1.6", "range": "", "iconUrl": ""}, {"name": "reduceBulletHurtBy", "cnName": "降低自身爆石类伤害", "father": "demonSkill", "index": "", "description": "", "cd": "", "effectType": "changeHurtNoCondition", "conditionType": "passive", "target": "me", "duration": "999999", "mul": "0.1", "range": "", "iconUrl": ""}, {"name": "先锋盾", "cnName": "先锋盾", "father": "demonSkill", "index": "", "description": "单位生命值大于[obj.per]时，受到的伤害减少[1-mul]。", "cd": "", "effectType": "changeHurt", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.3", "range": "", "iconUrl": ""}, {"name": "分身", "cnName": "分身", "father": "demonSkill", "index": "", "description": "产生1个分身，攻击力为自身的30%，血量为自身的[mul]，持续[duration]秒。", "cd": "20", "effectType": "cloned", "conditionType": "active", "target": "me", "duration": "20", "mul": "0.1", "range": "", "iconUrl": ""}, {"name": "demCloned2", "cnName": "超级分身", "father": "demonSkill", "index": "", "description": "产生[value]个分身，攻击力为自身的30%，血量为自身的[mul]，持续[duration]秒。", "cd": "20", "effectType": "cloned", "conditionType": "active", "target": "me", "duration": "20", "mul": "0.1", "range": "", "iconUrl": ""}, {"name": "weaponDefence", "cnName": "副手防御", "father": "demonSkill", "index": "", "description": "受到副手伤害减少[1-mul]。", "cd": "", "effectType": "changeHurtAndMul", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "weaponNo", "cnName": "副手抵抗", "father": "demonSkill", "index": "", "description": "受到副手伤害减少99.9%。", "cd": "", "effectType": "changeHurtAndMul", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.001", "range": "", "iconUrl": ""}, {"name": "fitVehicleDefence", "cnName": "聚合防御", "father": "demonSkill", "index": "", "description": "受到聚合载具的伤害减少[1-mul]。", "cd": "", "effectType": "changeHurtAndMul", "conditionType": "passive", "target": "target", "duration": "", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "immune", "cnName": "免疫", "father": "demonSkill", "index": "", "description": "当敌人对你释放技能时，有[effectProArr.0]几率使该技能对你无效。", "cd": "", "effectType": "immune", "conditionType": "passive", "target": "me", "duration": "", "mul": "1", "range": "", "iconUrl": ""}, {"name": "offAllSkill", "cnName": "封锁", "father": "demonSkill", "index": "", "description": "击中目标时封锁其所有技能2秒。", "cd": "", "effectType": "noAllSkill", "conditionType": "passive", "target": "target", "duration": "2", "mul": "", "range": "", "iconUrl": ""}, {"name": "offAllSkill5", "cnName": "封锁", "father": "demonSkill", "index": "", "description": "击中目标时封锁其所有技能5秒。", "cd": "", "effectType": "noAllSkill", "conditionType": "passive", "target": "target", "duration": "5", "mul": "", "range": "", "iconUrl": ""}, {"name": "offPassSkill", "cnName": "枷锁", "father": "demonSkill", "index": "", "description": "击中目标时封锁其被动技能[duration]秒，不会清除当前Buff状态。", "cd": "", "effectType": "noPassiveSkill", "conditionType": "passive", "target": "target", "duration": "2", "mul": "", "range": "", "iconUrl": ""}, {"name": "toLand", "cnName": "击落", "father": "demonSkill", "index": "", "description": "击中空中敌人时使其失去飞行能力。", "cd": "", "effectType": "toLand", "conditionType": "passive", "target": "target", "duration": "20", "mul": "", "range": "", "iconUrl": ""}, {"name": "toFlyCd", "cnName": "复飞", "father": "demonSkill", "index": "", "description": "进入飞行模式", "cd": "4", "effectType": "to<PERSON>ly", "conditionType": "active", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "underToLand", "cnName": "受击坠落", "father": "demonSkill", "index": "", "description": "受到飞行敌人攻击时，使敌人失去飞行能力。", "cd": "", "effectType": "toLand", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "统治圈", "cnName": "统治圈", "father": "demonSkill", "index": "", "description": "快速清除[range]码内敌人发射的卡特子弹，对无双及以上卡特无效。", "cd": "", "effectType": "clearEnemyFollowBullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "300", "iconUrl": ""}, {"name": "利刃盾", "cnName": "利刃盾", "father": "demonSkill", "index": "", "description": "敌人发起副手或特别近战攻击时，自身无敌[duration]秒。", "cd": "", "effectType": "noUnderAllB", "conditionType": "passive", "target": "me", "duration": "5", "mul": "", "range": "", "iconUrl": ""}, {"name": "利刃罩", "cnName": "利刃罩", "father": "demonSkill", "index": "", "description": "敌人发起副手或特别近战攻击时，自身无敌[duration]秒。最短触发间隔[minTriggerT]秒。", "cd": "", "effectType": "noUnderAllB", "conditionType": "passive", "target": "me", "duration": "5", "mul": "", "range": "", "iconUrl": ""}, {"name": "陨石雨", "cnName": "陨石雨", "father": "demonSkill", "index": "", "description": "对敌人发起大范围的陨石攻击。", "cd": "8", "effectType": "bullet", "conditionType": "active", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "大地闪电", "cnName": "大地闪电", "father": "demonSkill", "index": "", "description": "向周围释放扩散闪电，对目标造成巨大伤害，闪电还拥有无敌驱散效果。", "cd": "7", "effectType": "bullet_lightningFloor", "conditionType": "active", "target": "me", "duration": "6", "mul": "0.3", "range": "", "iconUrl": ""}, {"name": "破宠", "cnName": "破宠", "father": "demonSkill", "index": "", "description": "对尸宠造成2倍的伤害。", "cd": "", "effectType": "changeHurt_filterUnitType", "conditionType": "passive", "target": "target", "duration": "", "mul": "2", "range": "", "iconUrl": ""}, {"name": "blindnessSuper", "cnName": "超级致盲", "father": "demonSkill", "index": "", "description": "击中目标后使其失去准心，使其攻击成功率降低[mul]，持续4秒。", "cd": "", "effectType": "lost<PERSON><PERSON>", "conditionType": "passive", "target": "target", "duration": "4", "mul": "0.8", "range": "", "iconUrl": ""}, {"name": "电离驱散", "cnName": "电离驱散", "father": "demonSkill", "index": "", "description": "清除目标的电离折射、电离反转效果。", "cd": "", "effectType": "clearStateByBaseLabelArr", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "无敌驱散", "cnName": "无敌驱散", "father": "demonSkill", "index": "", "description": "清除目标身上大部分无敌效果。", "cd": "", "effectType": "clearStateByBaseLabelArr", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "超级散射", "cnName": "超级散射", "father": "demonSkill", "index": "", "description": "开启超级散射状态，持续[duration]秒。", "cd": "15", "effectType": "moreBullet", "conditionType": "active", "target": "me", "duration": "7", "mul": "3", "range": "", "iconUrl": ""}, {"name": "巨伤盾", "cnName": "巨伤盾", "father": "demonSkill", "index": "", "description": "关卡中抵挡5次百分比伤害。", "cd": "", "effectType": "noMulHurt", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "尸毒", "cnName": "尸毒", "father": "demonSkill", "index": "", "description": "自己被击毙后在原地产生区域毒气，经过的敌人将被封锁主动和被动技能、降低攻击力。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.01", "range": "", "iconUrl": ""}, {"name": "防空盾", "cnName": "防空盾", "father": "demonSkill", "index": "", "description": "不受飞行敌人的攻击。", "cd": "", "effectType": "noUnderFlyHit", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "激光盾", "cnName": "激光盾", "father": "demonSkill", "index": "", "description": "受到激光枪的伤害减少[1-mul]。", "cd": "", "effectType": "changeHurt_gunType", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.25", "range": "", "iconUrl": ""}, {"name": "复仇之箭", "cnName": "复仇之箭", "father": "demonSkill", "index": "", "description": "被击毙后向凶手发出夺命箭，夺命箭拥有无敌驱散效果。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "复仇之魂", "cnName": "复仇之魂", "father": "demonSkill", "index": "", "description": "被击毙后，在原地留下持续25秒的致命鬼魂，敌人（非载具）碰到后将受到巨大伤害。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.01", "range": "", "iconUrl": ""}, {"name": "夺命箭", "cnName": "夺命箭", "father": "demonSkill", "index": "", "description": "自身生命值每减少[secMul]就向敌人发出夺命箭，夺命箭拥有无敌驱散效果。", "cd": "", "effectType": "bullet_deadlyArrow", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "夺命魂", "cnName": "夺命魂", "father": "demonSkill", "index": "", "description": "自身生命值每减少[secMul]，就在原地留下持续25秒的夺命鬼魂，敌人（非载具）碰到后将受到巨大伤害。", "cd": "", "effectType": "bullet_deadly<PERSON><PERSON>t", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "瞬秒所有除了载具", "cnName": "瞬秒所有除了载具", "father": "demonSkill", "index": "", "description": "", "cd": "", "effectType": "killAllExcludeVehicle", "conditionType": "passive", "target": "target", "duration": "", "mul": "0.05", "range": "", "iconUrl": ""}, {"name": "反击导弹", "cnName": "反击导弹", "father": "demonSkill", "index": "", "description": "受到伤害就有几率向敌人发射导弹。", "cd": "", "effectType": "bullet_fightBackPoint", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "旋转电球", "cnName": "旋转电球", "father": "demonSkill", "index": "", "description": "身上永久环绕着一个可攻击敌人的闪电球，闪电球拥有电离驱散效果。", "cd": "", "effectType": "bullet", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "旋转电球", "cnName": "旋转电球", "father": "demonSkill", "index": "", "description": "身上永久环绕着一个可攻击敌人的闪电球，闪电球拥有电离驱散效果。主线任务、争霸无效。", "cd": "", "effectType": "bullet_screwBall2", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "短命之仇", "cnName": "短命之仇", "father": "demonSkill", "index": "", "description": "在出生[duration]秒内毙命，则会让凶手失去攻击力4秒（修罗冷门模式，攻击力只下降60%），无视技能免疫。", "cd": "", "effectType": "no", "conditionType": "passive", "target": "me", "duration": "5", "mul": "", "range": "", "iconUrl": ""}, {"name": "shortLivedDisabledUnderHit", "cnName": "短命之仇-受到攻击", "father": "demonSkill", "index": "", "description": "死后让凶手失去攻击力4秒（修罗冷门模式，攻击力只下降[secMul]）。", "cd": "", "effectType": "shortLivedDisabledUnderHit", "conditionType": "passive", "target": "target", "duration": "4", "mul": "0", "range": "", "iconUrl": ""}, {"name": "summonShortLife", "cnName": "折寿", "father": "demonSkill", "index": "", "description": "攻击中使敌方召唤单位的寿命损失加快。", "cd": "", "effectType": "dieCdSpeedMul", "conditionType": "passive", "target": "target", "duration": "9999", "mul": "3", "range": "", "iconUrl": ""}, {"name": "summonShortLifeMax", "cnName": "薄命", "father": "demonSkill", "index": "", "description": "攻击中使敌方召唤单位的寿命大幅降低，无视技能免疫。", "cd": "", "effectType": "dieCdSpeedMul", "conditionType": "passive", "target": "target", "duration": "9999", "mul": "20", "range": "", "iconUrl": ""}, {"name": "kill<PERSON>ll<PERSON><PERSON>mon", "cnName": "全域薄命", "father": "demonSkill", "index": "", "description": "击毙所有敌人召唤物。", "cd": "", "effectType": "kill<PERSON>ll<PERSON><PERSON>mon", "conditionType": "passive", "target": "me,range,enemy", "duration": "", "mul": "", "range": "999999", "iconUrl": ""}, {"name": "verShield", "cnName": "竖盾", "father": "demonSkill", "index": "", "description": "左右两侧竖起高高的护盾，抵挡任何攻击。", "cd": "", "effectType": "summonedUnitsAndCount", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "ver<PERSON><PERSON>eldBuff", "cnName": "竖盾buff", "father": "demonSkill", "index": "", "description": "", "cd": "", "effectType": "ver<PERSON><PERSON>eldBuff", "conditionType": "passive", "target": "me", "duration": "999999999", "mul": "", "range": "", "iconUrl": ""}, {"name": "midLightning", "cnName": "强电", "father": "demonSkill", "index": "", "description": "每隔[duration]秒，闪电攻击周围[range]码的敌人。", "cd": "", "effectType": "madCloseLightning", "conditionType": "passive", "target": "me,near,enemy", "duration": "0.25", "mul": "0.01", "range": "280", "iconUrl": ""}, {"name": "godShield", "cnName": "上帝之盾", "father": "demonSkill", "index": "", "description": "每[minTriggerT]秒抵挡1次必亡伤害。", "cd": "", "effectType": "godShield", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "lockLife", "cnName": "锁血", "father": "demonSkill", "index": "", "description": "每秒开放[mul]的扣血量。", "cd": "", "effectType": "lockLife", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.04", "range": "", "iconUrl": ""}, {"name": "killXinLing", "cnName": "瞬秒心零", "father": "demonSkill", "index": "", "description": "瞬秒心零。", "cd": "", "effectType": "kill", "conditionType": "passive", "target": "target", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "cantMove", "cnName": "监禁", "father": "demonSkill", "index": "", "description": "让所有敌人无法移动。无视技能免疫，不会被清除状态。", "cd": "", "effectType": "moveSpeed", "conditionType": "passive", "target": "me,range,enemy", "duration": "999999", "mul": "0", "range": "999999", "iconUrl": ""}, {"name": "noPurgoldArms", "cnName": "禁无双", "father": "demonSkill", "index": "", "description": "禁止敌人使用无双以及更高阶的武器（生肖武器除外）。", "cd": "", "effectType": "noPurgoldArms", "conditionType": "passive", "target": "me,range,enemy", "duration": "999999", "mul": "", "range": "999999", "iconUrl": ""}, {"name": "noRocket", "cnName": "禁火炮", "father": "demonSkill", "index": "", "description": "禁止敌人使用火炮武器。", "cd": "", "effectType": "noArmsType", "conditionType": "passive", "target": "me,range,enemy", "duration": "999999", "mul": "", "range": "999999", "iconUrl": ""}, {"name": "everToLand", "cnName": "随时击落", "father": "demonSkill", "index": "", "description": "让所有飞行敌人落地。", "cd": "", "effectType": "toLand", "conditionType": "passive", "target": "me,range,enemy", "duration": "", "mul": "", "range": "999999", "iconUrl": ""}, {"name": "衰竭", "cnName": "衰竭", "father": "demonSkill", "index": "", "description": "初始生命值设为[mul]。", "cd": "", "effectType": "setLifePer", "conditionType": "passive", "target": "me", "duration": "", "mul": "0.1", "range": "", "iconUrl": ""}, {"name": "blackHoleDemon", "cnName": "黑洞射线", "father": "demonSkill", "index": "", "description": "每[intervalT]秒清空一次[range]码内所有敌人的子弹。", "cd": "", "effectType": "blackHoleDemon", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "800", "iconUrl": ""}, {"name": "strollCard", "cnName": "闲逛", "father": "demonSkill", "index": "", "description": "瞬间移动到视野中的任何位置。", "cd": "3.5", "effectType": "teleport", "conditionType": "active", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "击中沉默", "cnName": "击中沉默", "father": "demonSkill", "index": "", "description": "击中目标后，有[effectProArr.0]的几率使其无法释放技能，持续[duration]秒，同时清除目标身上一些状态。", "cd": "", "effectType": "silenceBAndClearState", "conditionType": "passive", "target": "target", "duration": "4", "mul": "", "range": "", "iconUrl": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cnName": "同葬", "father": "demonSkill", "index": "", "description": "死后让所有友方单位一起陪葬。", "cd": "", "effectType": "to<PERSON><PERSON>", "conditionType": "passive", "target": "me,range,we", "duration": "", "mul": "", "range": "99999", "iconUrl": ""}, {"name": "fastCd", "cnName": "技能加速", "father": "demonSkill", "index": "", "description": "技能回复速度加快1倍。", "cd": "", "effectType": "cdMulAndMinTrigger", "conditionType": "passive", "target": "me", "duration": "999999", "mul": "0.5", "range": "", "iconUrl": ""}, {"name": "fastCd2", "cnName": "技能狂飙", "father": "demonSkill", "index": "", "description": "技能回复速度加快2倍。", "cd": "", "effectType": "cdMulAndMinTrigger", "conditionType": "passive", "target": "me", "duration": "999999", "mul": "0.3", "range": "", "iconUrl": ""}, {"name": "world180", "cnName": "颠倒世界", "father": "demonSkill", "index": "", "description": "颠倒世界。", "cd": "", "effectType": "world180", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "phantom<PERSON><PERSON>", "cnName": "集体照", "father": "demonSkill", "index": "", "description": "为[range]码范围内的我方单位（不包括角色、尸宠、载具、魂卡、分身）产生[value]个分身，持续[duration]秒。", "cd": "30", "effectType": "phantomDevice", "conditionType": "active", "target": "me,range,we", "duration": "30", "mul": "1", "range": "9999", "iconUrl": ""}, {"name": "worldBlack", "cnName": "暗无天日", "father": "demonSkill", "index": "", "description": "让世界处于完全的黑暗状态。", "cd": "", "effectType": "sightCover", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "rain1", "cnName": "小雨天气", "father": "demonSkill", "index": "", "description": "开场下小雨，持续[duration]秒，只在工厂中生效。地下、室内场景无法使用。", "cd": "", "effectType": "addWeatherEdit", "conditionType": "passive", "target": "me", "duration": "1800", "mul": "0.5", "range": "", "iconUrl": ""}, {"name": "rain2", "cnName": "中雨天气", "father": "demonSkill", "index": "", "description": "开场下中雨，持续[duration]秒，只在工厂中生效。地下、室内场景无法使用。", "cd": "", "effectType": "addWeatherEdit", "conditionType": "passive", "target": "me", "duration": "1800", "mul": "1.5", "range": "", "iconUrl": ""}, {"name": "rain3", "cnName": "大雨天气", "father": "demonSkill", "index": "", "description": "开场下大雨，持续[duration]秒，只在工厂中生效。地下、室内场景无法使用。", "cd": "", "effectType": "addWeatherEdit", "conditionType": "passive", "target": "me", "duration": "1800", "mul": "3", "range": "", "iconUrl": ""}, {"name": "heat1", "cnName": "高温天气", "father": "demonSkill", "index": "", "description": "开场高温天气，持续[duration]秒，只在工厂中生效。冰雪场景无法使用。", "cd": "", "effectType": "addWeatherEdit", "conditionType": "passive", "target": "me", "duration": "1800", "mul": "0.5", "range": "", "iconUrl": ""}, {"name": "heat2", "cnName": "炎热天气", "father": "demonSkill", "index": "", "description": "开场炎热天气，持续[duration]秒，只在工厂中生效。冰雪场景无法使用。", "cd": "", "effectType": "addWeatherEdit", "conditionType": "passive", "target": "me", "duration": "1800", "mul": "1.5", "range": "", "iconUrl": ""}, {"name": "heat3", "cnName": "酷热天气", "father": "demonSkill", "index": "", "description": "开场酷热天气，持续[duration]秒，只在工厂中生效。冰雪场景无法使用。", "cd": "", "effectType": "addWeatherEdit", "conditionType": "passive", "target": "me", "duration": "1800", "mul": "3", "range": "", "iconUrl": ""}, {"name": "killToSnakeTail", "cnName": "跟屁虫", "father": "demonSkill", "index": "", "description": "杀死第一个敌人将变成自己的跟屁虫。", "cd": "", "effectType": "killToSnakeTail", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "dieSnakeTail", "cnName": "跟屁虫-受伤", "father": "demonSkill", "index": "", "description": "", "cd": "", "effectType": "dieSnakeTail", "conditionType": "passive", "target": "me", "duration": "", "mul": "", "range": "", "iconUrl": ""}, {"name": "madFireBody", "cnName": "火种特效", "father": "demonSkill", "index": "", "description": "", "cd": "", "effectType": "no", "conditionType": "passive", "target": "me", "duration": "9999999", "mul": "", "range": "", "iconUrl": ""}]}