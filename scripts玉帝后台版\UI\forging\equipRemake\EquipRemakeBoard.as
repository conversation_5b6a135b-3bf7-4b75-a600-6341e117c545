package UI.forging.equipRemake
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.must.NormalMustBox;
   import UI.forging.equipUpgrade.EquipUpgradeBoard;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.creator.EquipRemakeCtrl;
   import dataAll.equip.creator.EquipSkillCreator;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.creator.OneProData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.SkillDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class EquipRemakeBoard extends NormalUI
   {
      
      private var copyData:EquipData;
      
      public var equipUpgradeBoard:EquipUpgradeBoard;
      
      private var proTag:Sprite;
      
      private var mustSp:Sprite;
      
      private var itemsGripSp:MovieClip;
      
      private var btnSp:MovieClip;
      
      private var gotoBackSp:MovieClip;
      
      private var proTxt:TextField;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var gotoBackBtn:NormalBtn = new NormalBtn();
      
      private var itemsGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var proBox:ItemsGripBox = new ItemsGripBox();
      
      private var _nowData:EquipData;
      
      private var nowProDataArr:Array = [];
      
      private var tempRemakeSave:EquipSave = null;
      
      private var beforeSave:EquipSave = null;
      
      public function EquipRemakeBoard()
      {
         super();
      }
      
      public function set nowData(da0:EquipData) : void
      {
         if(da0 != this._nowData || !da0)
         {
            this.nowProDataArr = [];
            this.beforeSave = null;
         }
         this.equipUpgradeBoard.nowData = da0;
         this._nowData = da0;
      }
      
      public function get nowData() : EquipData
      {
         return this.equipUpgradeBoard.nowData;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["proTag","mustSp","btnSp","gotoBackSp","itemsGripSp","proTxt"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("");
         addChild(this.gotoBackBtn);
         this.gotoBackBtn.setImg(this.gotoBackSp);
         this.gotoBackBtn.setName("编辑当前数据");
         addChild(this.itemsGrip);
         this.itemsGrip.setImg(this.itemsGripSp);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.itemsGrip);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         addChild(this.proBox);
         NormalUICtrl.setTag(this.proBox,this.proTag);
         this.proBox.arg.init(1,8,0,4);
         this.proBox.evt.setWantEvent(true,false,false,true,true);
         this.proBox.setIconPro("ForgingUI/armsProBar",50,50);
         this.proBox.addEventListener(ClickEvent.ON_CLICK,this.proBarClick);
         this.proBox.addEventListener(ClickEvent.ON_OVER,this.proBarOver);
         this.proBox.addEventListener(ClickEvent.ON_OUT,this.proBarOut);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.btn.addEventListener(MouseEvent.MOUSE_OUT,this.proBarOut);
         this.gotoBackBtn.addEventListener(MouseEvent.CLICK,this.gotoBackBtnClick);
         this.gotoBackBtn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.gotoBackBtn.addEventListener(MouseEvent.MOUSE_OUT,this.proBarOut);
         this.showNone();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"equip");
         this.showOneEquipDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:EquipData) : void
      {
         if(visible)
         {
            this.showOneEquipDataAndPan(da0);
         }
      }
      
      private function proBarClick(e:ClickEvent) : void
      {
         if(this.proBox.gripArr.length == 1)
         {
            return;
         }
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var da0:OneProData = e.childData as OneProData;
         if(!da0.noChangeLockB)
         {
            da0.lockB = !da0.lockB;
         }
         grip0.inData_proData(da0);
         this.fleshMust();
      }
      
      private function proBarOver(e:ClickEvent) : void
      {
         var d0:SkillDefine = null;
         var str0:String = null;
         this.proBarOut(e);
         var da0:OneProData = e.childData as OneProData;
         if(da0.type == "skill")
         {
            d0 = Gaming.defineGroup.skill.getDefine(da0.name);
            str0 = ComMethod.color("<b>" + d0.cnName + "</b>","#FFFF00");
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0 + "\n" + d0.getDescription());
         }
      }
      
      private function proBarOut(e:Event) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function btnOver(e:Event) : void
      {
         var str0:String = "";
         if(e.target != this.btn)
         {
            if(e.target == this.gotoBackBtn)
            {
               str0 = "除了装备时装外其他equip不得编辑！";
            }
         }
         if(str0 == "")
         {
            this.proBarOut(e);
         }
         else
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      private function showOneEquipDataAndPan(da0:EquipData, fleshNowProDataArrB0:Boolean = false) : void
      {
         var dg0:EquipDataGroup = null;
         this.btn.setName("复制装备");
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要复制的装备。");
         if(this.copyData != null)
         {
            this.btn.actived = true;
            this.btn.setName("添加装备");
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
         if(da0)
         {
            dg0 = Gaming.PG.da.findEquipData(da0,false);
            if(dg0 is EquipDataGroup)
            {
               this.showOneEquipData(da0,fleshNowProDataArrB0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneEquipData(da0:EquipData, fleshNowProDataArrB0:Boolean = false) : void
      {
         this.nowData = da0;
         this.itemsGrip.inData_equip(da0);
         if(fleshNowProDataArrB0 || this.nowProDataArr.length == 0)
         {
            this.nowProDataArr = EquipSkillCreator.getOneProDataArr(da0.save);
         }
         this.proBox.inData_byArr(this.nowProDataArr,"inData_proData");
         if(this.nowProDataArr.length == 1)
         {
            this.proBox.setAllFun("setShopBtnBackMc","");
         }
         this.fleshMust();
      }
      
      private function fleshMust() : void
      {
         var s0:EquipSave = this.nowData.save;
         var must_d0:MustDefine = EquipRemakeCtrl.getMust(this.nowData.save,this.nowProDataArr);
         var mustB0:Boolean = this.mustBox.inData(must_d0);
         var lockAllB0:Boolean = this.proIsLockAllB();
         var colorB0:Boolean = EquipColor.firstMax(this.nowData.getColor(),"orange",true);
         this.btn.actived = true;
         this.gotoBackBtn.visible = true;
         this.proTxt.text = "";
         if(!colorB0)
         {
            this.proTxt.htmlText = ComMethod.color("点击复制装备即可复制当前装备","#FF0000");
         }
         else if(this.nowProDataArr.length == 0)
         {
            this.proTxt.text = "";
         }
      }
      
      private function proIsLockAllB() : Boolean
      {
         var da0:OneProData = null;
         if(this.nowProDataArr.length == 0)
         {
            return false;
         }
         for each(da0 in this.nowProDataArr)
         {
            if(!da0.lockB)
            {
               return false;
            }
         }
         return true;
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         this.beforeSave = null;
         this.nowProDataArr = [];
         this.tempRemakeSave = null;
         this.itemsGrip.clearData();
         this.proBox.inData_byArr([],"inData_proData");
         this.mustBox.setShowState(false);
         this.btn.actived = true;
         this.gotoBackBtn.visible = true;
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var s0:EquipSave = new EquipSave();
         if(this.copyData == null)
         {
            this.copyData = this.nowData;
            Gaming.uiGroup.alertBox.showSuccess("复制当前装备数据成功！");
            this.showOneEquipDataAndPan(this.nowData);
         }
         else
         {
            s0.inData_byObj(this.copyData.save);
            Gaming.PG.da.equipBag.addSave(s0);
            Gaming.uiGroup.alertBox.showSuccess("添加装备成功！");
            this.copyData = null;
            this.showOneEquipDataAndPan(this.nowData);
         }
      }
      
      private function gotoBackBtnClick(e:MouseEvent) : void
      {
         var s0:EquipSave = new EquipSave();
         s0.inData_byObj(this.nowData.save);
         if(this.nowData)
         {
            if(s0.partType == "fashion" || s0.partType == "coat" || s0.partType == "pants" || s0.partType == "head" || s0.partType == "belt")
            {
               this.showEquipEditMenu();
            }
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("装备数据不存在！");
         }
      }
      
      private function EquipEdit_Equip(str0:String) : void
      {
         var i:int = 0;
         var j:int = 0;
         var ArrColor0:Array = ["white","green","blue","purple","orange","red","black","darkgold","purgold","yagold"];
         var ArrColor1:Array = ["白","绿","蓝","紫","橙","红","黑","暗金","紫金","氩星"];
         var Arr_type0:Array = ["coat","pants","head","belt","fashion"];
         var Arr_type1:Array = ["衣服","裤子","头盔","腰带","时装"];
         var s0:EquipSave = new EquipSave();
         var ArrNum:Array = new Array();
         var ArrNow:Array = new Array();
         var EquipNow:String = null;
         ArrNum = str0.split("&",str0.length);
         s0.inData_byObj(this.nowData.save);
         for each(EquipNow in ArrNum)
         {
            ArrNow = EquipNow.split("*",EquipNow.length);
            if(ArrNow[0] == "00")
            {
               s0.addLevel = Number(ArrNow[1]);
            }
            else if(ArrNow[0] == "01")
            {
               i = 0;
               while(i < ArrColor0.length)
               {
                  if(ArrNow[1] == ArrColor1[i])
                  {
                     s0.color = ArrColor0[i];
                  }
                  i++;
               }
            }
            else if(ArrNow[0] == "02")
            {
               s0.strengthenLv = Number(ArrNow[1]);
            }
            else if(ArrNow[0] == "03")
            {
               s0.evoLv = Number(ArrNow[1]);
            }
            else if(ArrNow[0] == "04")
            {
               j = 0;
               while(j < Arr_type0.length)
               {
                  if(ArrNow[1] == Arr_type1[j])
                  {
                     s0.partType = Arr_type0[j];
                  }
                  j++;
               }
            }
            else if(ArrNow[0] == "05")
            {
               s0.itemsLevel = Number(ArrNow[1]);
            }
            else if(ArrNow[0] == "06")
            {
               s0.getTime = ArrNow[1];
            }
            else if(ArrNow[0] == "07")
            {
               s0.severTime = ArrNow[1];
            }
            else if(ArrNow[0] == "08")
            {
               s0.severTime = "9999-9-9 12:00:00";
            }
            else if(ArrNow[0] == "09" || ArrNow[0] == "技能添加")
            {
               if(ArrNow[1] == "所有" || ArrNow[1] == "全部" || ArrNow[1] == "all")
               {
                  // 添加所有装备技能
                  var allSkillArr:Array = this.getAllEquipSkills(s0.partType);
                  if(allSkillArr.length > 0)
                  {
                     // 确保skillArr已初始化
                     if(!s0.skillArr) s0.skillArr = [];

                     // 添加技能，避免重复
                     for(var k:int = 0; k < allSkillArr.length; k++)
                     {
                        if(s0.skillArr.indexOf(allSkillArr[k]) == -1)
                        {
                           s0.skillArr.push(allSkillArr[k]);
                        }
                     }
                     Gaming.uiGroup.alertBox.showSuccess("已添加所有适合" + s0.partType + "的技能，共" + allSkillArr.length + "个安全技能！");
                  }
                  else
                  {
                     Gaming.uiGroup.alertBox.showError("该装备类型没有可用的技能！");
                  }
               }
               else
               {
                  if(!s0.skillArr) s0.skillArr = [];
                  s0.skillArr.push(ArrNow[1]);
                  Gaming.uiGroup.alertBox.showSuccess("已添加技能：" + ArrNow[1]);
               }
            }
            else if(ArrNow[0] == "10" || ArrNow[0] == "技能删除")
            {
               if(s0.skillArr && s0.skillArr.indexOf(ArrNow[1]) >= 0)
               {
                  s0.skillArr.splice(s0.skillArr.indexOf(ArrNow[1]),1);
                  Gaming.uiGroup.alertBox.showSuccess("已删除技能：" + ArrNow[1]);
               }
               else
               {
                  Gaming.uiGroup.alertBox.showError("技能不存在：" + ArrNow[1]);
               }
            }
            else if(ArrNow[0] == "11" || ArrNow[0] == "copyData")
            {
               this.copyData = null;
               Gaming.uiGroup.alertBox.showSuccess("copyData = null");
            }
            // 扩展属性编辑功能 (12-42)
            else if(ArrNow[0] == "12" || ArrNow[0] == "无双水晶掉落")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["demStroneDropNum"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置无双水晶掉落：" + ArrNow[1] + "个");
            }
            else if(ArrNow[0] == "13" || ArrNow[0] == "万能球掉落")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["demBallDropNum"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置万能球掉落：" + ArrNow[1] + "个");
            }
            else if(ArrNow[0] == "14" || ArrNow[0] == "战神之心掉落")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["madheartDropNum"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置战神之心掉落：" + ArrNow[1] + "个");
            }
            else if(ArrNow[0] == "15" || ArrNow[0] == "幸运值")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["lottery"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置幸运值：" + ArrNow[1]);
            }
            else if(ArrNow[0] == "16" || ArrNow[0] == "商运掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["coinMul"] = Number(ArrNow[1]) / 100; // 转换为倍率
               Gaming.uiGroup.alertBox.showSuccess("设置商运掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "17" || ArrNow[0] == "优胜券获取")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["arenaStampDropNum"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置优胜券获取：" + ArrNow[1] + "个");
            }
            else if(ArrNow[0] == "18" || ArrNow[0] == "载具碎片掉落")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["vehicleCashDropNum"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置载具碎片掉落：" + ArrNow[1] + "个");
            }
            else if(ArrNow[0] == "19" || ArrNow[0] == "生命催化剂掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["lifeCatalystDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置生命催化剂掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "20" || ArrNow[0] == "神能石掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["godStoneDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置神能石掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "21" || ArrNow[0] == "转化石掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["converStoneDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置转化石掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "22" || ArrNow[0] == "化石掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["taxStampDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置化石掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "23" || ArrNow[0] == "血手掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["bloodStoneDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置血手掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "24" || ArrNow[0] == "装置掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["deviceDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置装置掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "25" || ArrNow[0] == "赠礼好感度")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["loveAdd"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置赠礼好感度：" + ArrNow[1] + "点");
            }
            else if(ArrNow[0] == "26" || ArrNow[0] == "好感度每天")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["dayLoveAdd"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置每日好感度：" + ArrNow[1] + "点");
            }
            else if(ArrNow[0] == "27" || ArrNow[0] == "战斗力神级")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["dpsAll"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置战斗力/神级：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "28" || ArrNow[0] == "防弹值")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["bulletDedut"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置防弹值：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "29" || ArrNow[0] == "每日扫荡次数")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["sweepingNum"] = Number(ArrNow[1]);
               Gaming.uiGroup.alertBox.showSuccess("设置每日扫荡次数：" + ArrNow[1] + "次");
            }
            else if(ArrNow[0] == "30" || ArrNow[0] == "经验获取")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["exp"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置经验获取：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "31" || ArrNow[0] == "经验获取VIP")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["expVip"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置VIP经验获取：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "32" || ArrNow[0] == "武器碎片掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["weaponDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置武器碎片掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "33" || ArrNow[0] == "装备碎片掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["blackEquipDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置装备碎片掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "34" || ArrNow[0] == "随机武器掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["ranBlackArmsDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置随机武器掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "35" || ArrNow[0] == "稀有装备掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["rareEquipDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置稀有装备掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "36" || ArrNow[0] == "特殊零件掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["specialPartsDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置特殊零件掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "37" || ArrNow[0] == "宝石掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["gemDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置宝石掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "38" || ArrNow[0] == "尸宠图鉴掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["petBookDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置尸宠图鉴掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "39" || ArrNow[0] == "红橙基因体掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["rareGeneDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置红橙基因体掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "40" || ArrNow[0] == "副手掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["blackArmsDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置副手掉率：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "41" || ArrNow[0] == "银币获取")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["coinMul"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置银币获取：" + ArrNow[1] + "%");
            }
            else if(ArrNow[0] == "42" || ArrNow[0] == "稀有武器掉率")
            {
               if(!s0.obj) s0.obj = {};
               s0.obj["rareArmsDropPro"] = Number(ArrNow[1]) / 100;
               Gaming.uiGroup.alertBox.showSuccess("设置稀有武器掉率：" + ArrNow[1] + "%");
            }

         }
         this.nowData.save = s0;

         // 强制刷新装备数据和显示
         this.nowData.save.fleshSMaxLv();
         if(this.nowData.placeType == "wear")
         {
            this.nowData.normalPlayerData.fleshAllByEquip();
         }

         // 重新计算属性数据
         var nowProDataArr2:Array = EquipSkillCreator.getOneProDataArr(this.nowData.save);
         OneProData.setLockByOther(nowProDataArr2,this.nowProDataArr);
         this.nowProDataArr = nowProDataArr2;

         // 调试信息：显示obj内容
         if(s0.obj != null)
         {
            var debugInfo:String = "装备obj属性: ";
            for(var prop:String in s0.obj)
            {
               debugInfo += prop + "=" + s0.obj[prop] + " ";
            }
            Gaming.uiGroup.alertBox.showSuccess(debugInfo);
         }

         // 强制重新初始化属性创建器
         try {
            Gaming.defineGroup.equipPropertyDataCreator.inAllProArr();
         } catch(e:Error) {
            // 忽略错误
         }

         // 刷新显示
         this.showOneEquipDataAndPan(this.nowData);
         Gaming.uiGroup.allBagUI.fleshAllBox();

         Gaming.uiGroup.alertBox.showSuccess("装备属性编辑成功！");
      }
      
      private function getAllEquipSkills(partType:String) : Array
      {
         // 返回231个安全技能，已过滤掉不适合的技能
         var allSkills:Array = [
               "群体圣光","狂暴","反击","派生导弹","吞噬","定点轰炸","远视","群体隐身","万弹归宗","金刚钻",
               "毒雾","嗜爪","欺凌","魅惑","电离折射","馈赠","沉默","近视","先锋盾","全局溅射",
               "群体自燃","精准","FoggyHero","藐视","元素叠加","血盾","滑翔-不可被重造","rolling_hero","王者之翼","附身",
               "silverScreen_hero","隐匿之雾","尖叫","反转术","全域圣光","妖魅","wisdomAnger_hero","技能复制","尸化","毒雾-减血",
               "吞噬-回血","反馈-反弹被动技能","selfBurn_hero_link","wisdomAnger_hero_link","rolling_hero_link","沉默-使我方技能免疫3秒","moreMissile_hero_dizziness","moreMissile_hero_dizziness2","战争抗体","outfit_blood",
               "猎手原形","悦动先锋","饥饿行踪","血脉沸腾","沃龙隐","沃龙隐-攻击力加成","鹰眼","遁形","核爆","gaze_peak",
               "sacrifice_peak","怒蜂","群蜂","hurtAddYing","trueshotYing","竞技场-伤害加倍","击中减速","减速光环","反弹伤害","超级散射",
               "无限馈赠","猎击","酸性腐蚀","爆裂弹","月饼子弹","修罗弹夹","狂战尸-旋风刀","狂战尸-震地","狂战尸-狂刃追踪","远视",
               "橄榄僵尸-弹性世界","全域圣光","无尽轰炸","灼热视线-眩晕","闪烁-目标点爆炸","电离反转","定点轰炸","欺凌","人类克星","恶魔猎手",
               "恶魔屠刀","godHand_equip","immune_equip","magneticField_equip","strongHalo_equip","murderous_equip","poisonRange_equip","attackSpeedHalo_equip","sacrifice_equip","backStrong_equip",
               "anionSkin_equip","treater_equip","backWeak_equip","thornSkin_equip","refraction_equip","zoomOut","refraction_equip_link","合金护甲","真空-无敌","敏捷馈赠",
               "防御力提升","攻击力提升","真情之力","回复","真情治愈","嘲讽","遇强则刚","复仇","共鸣","暗夜信徒",
               "恶爪","击毙补充弹药","击毙回复","剧毒","击毙溅射","振奋","击中溅射","致残","击中麻痹","致盲",
               "击中回复","击中沉默","击中派生","震动炮","背刺","implodingGod","裂空波","穿刺","弹力回收","盛血绞杀",
               "膝跳反应","静电反应","迷失电流","共振","老练","远射","超重","瞬秒","绝灭","快感",
               "搏命","七步毒","击中闪电","击中眩晕","超级派生","超级减速","超级麻痹","炎爆","冷凝","蚀骨",
               "痛击","连弩","超级粘性","火焰始祖","贯穿波","无限火力","跳斩","上帝之杖","风雷","影灭",
               "爆胆","爆震","飞镰","跟踪飞镰","战修罗","突袭","惩罚","打滑","超级共振","陈年老糖",
               "editBulletPath","炎爆-爆","轰天聚合","镇山聚合","霸空聚合","核弹头","核动力","守望之盾","合金外壳","狂战射手-击中麻痹",
               "特效","防御力提升30%","weaponEmp","weaponEmpAll","凯撒特效附带","先锋盾","分身","统治圈","利刃盾","利刃罩",
               "陨石雨","大地闪电","破宠","电离驱散","无敌驱散","巨伤盾","尸毒","防空盾","激光盾","复仇之箭",
               "复仇之魂","夺命箭","夺命魂","瞬秒所有除了载具","反击导弹","旋转电球","短命之仇","衰竭","击中沉默"
            ];

         return allSkills;
      }

      // 分页装备编辑菜单
      private function showEquipEditMenu() : void
      {
         var menuText:String = "装备编辑菜单\n\n";
         menuText += "【1】基础属性编辑\n";
         menuText += "【2】技能管理编辑\n";
         menuText += "【3】时间设置编辑\n";
         menuText += "【4】掉落物品编辑\n";
         menuText += "【5】掉率属性编辑\n";
         menuText += "【6】经验好感编辑\n";
         menuText += "【7】战斗属性编辑\n\n";
         menuText += "请输入页面编号(1-7)或直接输入指令：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(menuText, "", this.onEditMenuSelect);
      }

      // 处理编辑菜单选择
      private function onEditMenuSelect(selection:String) : void
      {
         var pageNum:int = parseInt(selection);

         switch(pageNum)
         {
            case 1:
               this.showBasicEditPage();
               break;
            case 2:
               this.showSkillEditPage();
               break;
            case 3:
               this.showTimeEditPage();
               break;
            case 4:
               this.showDropItemEditPage();
               break;
            case 5:
               this.showDropRateEditPage();
               break;
            case 6:
               this.showExpLoveEditPage();
               break;
            case 7:
               this.showBattleEditPage();
               break;
            default:
               // 直接执行指令
               this.EquipEdit_Equip(selection);
               break;
         }
      }

      // 页面1：基础属性编辑
      private function showBasicEditPage() : void
      {
         var helpText:String = "基础属性编辑\n\n";
         helpText += "00*数值 或 等级*数值      # 设置装备等级\n";
         helpText += "01*颜色 或 颜色*颜色      # 设置装备品质\n";
         helpText += "02*数值 或 强化*数值      # 设置强化等级\n";
         helpText += "03*数值 或 进化*数值      # 设置进化等级\n";
         helpText += "04*类型 或 类型*类型      # 设置装备类型\n";
         helpText += "05*数值 或 基础等级*数值   # 设置基础等级\n\n";
         helpText += "颜色: 白绿蓝紫橙红黑暗金紫金氩星\n";
         helpText += "类型: 衣服裤子头盔腰带时装\n\n";
         helpText += "示例: 00*99&01*红&02*20&03*5\n\n";
         helpText += "请输入编辑指令：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }

      // 页面2：技能管理编辑
      private function showSkillEditPage() : void
      {
         var helpText:String = "技能管理编辑\n\n";
         helpText += "09*技能 或 技能添加*技能   # 添加单个技能\n";
         helpText += "09*全部 或 技能添加*全部   # 添加所有231个安全技能\n";
         helpText += "10*技能 或 技能删除*技能   # 删除技能\n\n";
         helpText += "现在支持231个安全技能（已过滤不适合的技能），包括:\n";
         helpText += "• 英雄技能: 群体圣光 狂暴 反击 派生导弹等\n";
         helpText += "• 装备技能: godHand_equip immune_equip等\n";
         helpText += "• 宠物技能: 旋风刀 震地 狂刃追踪等\n";
         helpText += "• 武器技能: 击中眩晕 超级派生等\n\n";
         helpText += "示例: 09*全部 (添加所有技能)\n";
         helpText += "      09*群体圣光 (添加单个技能)\n\n";
         helpText += "请输入编辑指令：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }

      // 页面3：时间设置编辑
      private function showTimeEditPage() : void
      {
         var helpText:String = "时间设置编辑\n\n";
         helpText += "06*时间 或 获取时间*时间   # 设置获取时间\n";
         helpText += "07*时间 或 到期时间*时间   # 设置到期时间\n";
         helpText += "08 或 永不过期           # 设置永不过期\n";
         helpText += "11 或 copyData          # 清除复制数据\n\n";
         helpText += "时间格式: 2025-05-20 12:00:00\n\n";
         helpText += "示例: 08 或 07*2025-12-31 23:59:59\n\n";
         helpText += "请输入编辑指令：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }










      private function affterGotoBack() : void
      {
         this.remakeBySave(this.beforeSave);
         this.beforeSave = null;
         Gaming.soundGroup.playSound("uiSound","getItems");
      }
      
      private function affterGetStoreState(v0:int) : void
      {
         var must_d0:MustDefine = null;
         if(v0 == 1 || v0 == -2)
         {
            Gaming.uiGroup.connectUI.hide();
            must_d0 = EquipRemakeCtrl.getMust(this.nowData.save,this.nowProDataArr);
            PlayerMustCtrl.deductMust(must_d0,this.afterRemake);
         }
      }
      
      private function remakeBySave(s0:EquipSave) : void
      {
         EquipRemakeCtrl.setAllByOther(this.nowData.save,s0);
         if(this.nowData.placeType == "wear")
         {
            this.nowData.normalPlayerData.fleshAllByEquip();
         }
         var nowProDataArr2:Array = EquipSkillCreator.getOneProDataArr(this.nowData.save);
         OneProData.setLockByOther(nowProDataArr2,this.nowProDataArr);
         this.nowProDataArr = nowProDataArr2;
         this.showOneEquipDataAndPan(this.nowData,false);
         Gaming.uiGroup.allBagUI.fleshAllBox();
      }
      
      private function afterRemake() : void
      {
         this.beforeSave = new EquipSave();
         this.beforeSave.inData_byObj(this.nowData.save);
         this.remakeBySave(this.tempRemakeSave);
         ++Gaming.PG.save.headCount.equipRemakeNum;
         UIOrder.save(true,false,false,null,null,false,true);
      }

      // 页面4：掉落物品编辑
      private function showDropItemEditPage() : void
      {
         var helpText:String = "掉落物品编辑\n\n";
         helpText += "12*数值 或 无双水晶掉落*数值    # 设置无双水晶掉落数量\n";
         helpText += "13*数值 或 万能球掉落*数值      # 设置万能球掉落数量\n";
         helpText += "14*数值 或 战神之心掉落*数值    # 设置战神之心掉落数量\n";
         helpText += "15*数值 或 幸运值*数值          # 设置幸运值\n";
         helpText += "16*数值 或 商运掉率*数值        # 设置银币获取倍率(%)\n";
         helpText += "17*数值 或 优胜券获取*数值      # 设置优胜券获取数量\n";
         helpText += "18*数值 或 载具碎片掉落*数值    # 设置载具碎片掉落数量\n\n";
         helpText += "示例: 12*999&13*999&14*999&15*999&16*99900\n\n";
         helpText += "请输入编辑指令：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }

      // 页面5：掉率属性编辑
      private function showDropRateEditPage() : void
      {
         var helpText:String = "掉率属性编辑\n\n";
         helpText += "19*数值 或 生命催化剂掉率*数值  # 设置生命催化剂掉率(%)\n";
         helpText += "20*数值 或 神能石掉率*数值      # 设置神能石掉率(%)\n";
         helpText += "21*数值 或 转化石掉率*数值      # 设置转化石掉率(%)\n";
         helpText += "22*数值 或 化石掉率*数值        # 设置化石掉率(%)\n";
         helpText += "23*数值 或 血手掉率*数值        # 设置血手掉率(%)\n";
         helpText += "24*数值 或 装置掉率*数值        # 设置装置掉率(%)\n";
         helpText += "32*数值 或 武器碎片掉率*数值    # 设置武器碎片掉率(%)\n";
         helpText += "33*数值 或 装备碎片掉率*数值    # 设置装备碎片掉率(%)\n";
         helpText += "37*数值 或 宝石掉率*数值        # 设置宝石掉率(%)\n\n";
         helpText += "示例: 19*99900&20*99900&21*99900&22*99900\n\n";
         helpText += "请输入编辑指令：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }

      // 页面6：经验好感编辑
      private function showExpLoveEditPage() : void
      {
         var helpText:String = "经验好感编辑\n\n";
         helpText += "25*数值 或 赠礼好感度*数值      # 设置赠礼好感度点数\n";
         helpText += "26*数值 或 好感度每天*数值      # 设置每日好感度点数\n";
         helpText += "29*数值 或 每日扫荡次数*数值    # 设置每日扫荡次数\n";
         helpText += "30*数值 或 经验获取*数值        # 设置经验获取倍率(%)\n";
         helpText += "31*数值 或 经验获取VIP*数值     # 设置VIP经验获取倍率(%)\n\n";
         helpText += "示例: 25*999&26*999&29*999&30*999\n\n";
         helpText += "请输入编辑指令：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }

      // 页面7：战斗属性编辑
      private function showBattleEditPage() : void
      {
         var helpText:String = "战斗属性编辑\n\n";
         helpText += "27*数值 或 战斗力神级*数值      # 设置战斗力/神级(%)\n";
         helpText += "28*数值 或 防弹值*数值          # 设置防弹值(%)\n\n";
         helpText += "示例: 27*15&28*39\n\n";
         helpText += "请输入编辑指令：";

         Gaming.uiGroup.alertBox.textInput.showTextInput(helpText, "", this.EquipEdit_Equip);
      }
   }
}

